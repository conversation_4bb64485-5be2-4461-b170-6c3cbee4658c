import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/video_card.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/product/components/goods_spec.dart';
import 'package:chilat2_mall_app/pages/product/product_model.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';

class ProductPage extends StatefulWidget {
  // final String productId;
  const ProductPage({super.key});

  @override
  State<ProductPage> createState() => _ProductPageState();
}

class _ProductPageState extends State<ProductPage> {
  int _selectedIndex = 0;
  bool _isLoading = true; // 标记数据是否正在加载
  // MidCartModel cartData = MidCartModel(goodsList: []);
  MidCartModel? cartData;
  GoodsInfoModel? pageData;
  Map<String, List<String>> selectedSpecsMap = {};
  Map<String, int> selectedCountMap = {};
  List<SkuItem> selectedSkuList = [];
  double screenTop = 0.0;
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  int selectedSkuQty = 0;
  double selectedSkuPrice = 0.0;
  List<String> selectedSpecItemList = [];
  Map<String, SkuSpecItem> selectedSpecItemMap = {};

  @override
  void initState() {
    super.initState();

    onPageData();
    onCartData();
  }

  Future<void> onPageData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      dynamic res = await ProductAPI.useGoodsDetail({
        "id": Get.arguments['productId'],
        "deviceType": 1,
      });

      if (res?['result']?['code'] == 200) {
        setState(() {
          // cartData = MidCartModel.fromJson(res['data']?['cartInfo']);
          pageData = GoodsInfoModel.fromJson(res['data']?['pageData']);
          if (pageData?.videoUrl?.isNotEmpty == true) {
            pageData?.goodsImageList?.insert(0,
                "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/02/18/b141556a-bb38-4c56-9967-88416b7ee6d0.png");
          }
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> onCartData() async {
    MidCartModel? result = await Global.getCartData();
    setState(() {
      cartData = result;
    });
  }

  String onGetStepPrice(int index) {
    GoodsPriceRange? price = pageData?.goodsPriceRanges?[index];
    if (price?.minPrice != price?.maxPrice) {
      return '${setUnit(price?.minPrice ?? 0)}-${setUnit(price?.maxPrice ?? 0)}';
    } else {
      return setUnit(price?.maxPrice ?? 0);
    }
  }

  String onPriceRangeDesc(int index) {
    GoodsPriceRange? price = pageData?.goodsPriceRanges?[index];
    if (price?.end == -1) {
      return '>=${price?.start} ${pageData?.goodsPriceUnitName}';
    } else {
      if (price?.start == price?.end) {
        return '${price?.start} ${pageData?.goodsPriceUnitName}';
      }
      return '${price?.start}-${price?.end} ${pageData?.goodsPriceUnitName}';
    }
  }

  // 获取规格名称
  String onSpecName() {
    return (pageData?.specList ?? []).map((item) => item.name).join('·');
  }

  // SKU列表的背景色
  Color getSkuItemBgColor(List<SkuSpecItem> specItems) {
    List<String> itemIds = [];
    selectedSpecsMap.forEach((key, value) {
      itemIds.addAll(value);
    });
    var count = specItems.fold(0, (prev, item) {
      if (itemIds.contains(item.itemId)) {
        return prev + 1;
      }
      return prev;
    });
    return count == pageData?.specList.length
        ? Colors.red.shade100
        : Colors.grey.shade100;
  }

  // 购物车列表
  void onGotoCartList(BuildContext context) async {
    NavigatorUtil.pushNamed(context, AppRoutes.CartPage);
  }

  @override
  Widget build(BuildContext context) {
    screenTop = MediaQuery.paddingOf(context).top;
    screenWidth = MediaQuery.sizeOf(context).width;
    screenHeight = MediaQuery.sizeOf(context).height;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            showScrollToTopButton: true,
            backgroundColor: Colors.white,
            scrollToTopThreshold: 200,
            body: Column(
              children: [
                SearchHeader(
                  type: SearchHeaderType.backHeader,
                  showBackIcon: true,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // 待优化
                        _buildSelectedImage(),
                        _buildGoodsImageList(),
                        // 商品价格和描述
                        _buildPriceRangeAndDesc(),
                        // 商品规格
                        _buildProductSpec(),
                        // 商品属性
                        _buildProductAttr(),
                        // 商品详情
                        _buildProductDetail(),
                      ],
                    ),
                  ),
                ),
                _buildFooterBar()
              ],
            ));
  }

  @override
  Widget build2(BuildContext context) {
    screenTop = MediaQuery.of(context).padding.top;
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            backgroundColor: Colors.grey.shade200,
            body: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  _buildHeaderBar(),
                  // 商品视频和图片
                  _buildSelectedImage(),
                  _buildGoodsImageList(),
                  // 商品价格和描述
                  _buildPriceRangeAndDesc(),
                  // 商品规格
                  _buildProductSpec(),
                  // 商品属性
                  _buildProductAttr(),
                  // 商品详情
                  _buildProductDetail(),
                ],
              ),
            ),
            // 底部添加购物车按钮
            bottomNavigationBar: _buildFooterBar());
  }

  // 商品详情头部栏
  Widget _buildHeaderBar() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
      // padding:
      //     EdgeInsets.only(left: 2.0, right: 8.0, top: screenTop, bottom: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: const Icon(
              Icons.arrow_back,
              size: 24,
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text("商品详情"),
          )
        ],
      ),
    );
  }

  // 选择的图片/视频
  Widget _buildSelectedImage() {
    return Container(
      height: screenHeight * 0.4,
      color: Colors.white,
      child: _selectedIndex == 0 && pageData?.videoUrl?.isNotEmpty == true
          ? VideoCard(
              top: screenHeight * 0.15,
              videoUrl: pageData?.videoUrl ?? '',
            )
          : CachedNetworkImage(
              imageUrl: pageData?.goodsImageList?[_selectedIndex] ?? '',
              fit: BoxFit.cover,
              width: double.infinity,
            ),
    );
  }

  Widget _buildGoodsImageList() {
    return Container(
      height: 70,
      margin: EdgeInsets.only(top: 2),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: pageData?.goodsImageList?.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedIndex = index;
              });
            },
            child: Container(
              margin: EdgeInsets.all(2),
              decoration: BoxDecoration(
                border: Border.all(
                  color: _selectedIndex == index
                      ? const Color.fromARGB(255, 243, 33, 33)
                      : Colors.grey.shade500,
                  width: _selectedIndex == index ? 1.25 : 0.5,
                ),
              ),
              child: CachedNetworkImage(
                imageUrl: pageData?.goodsImageList?[index] ?? '',
                width: 70,
                height: 70,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ),
    );
  }

  // 阶梯价格和描述
  Widget _buildPriceRangeAndDesc() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.sp),
      child: Container(
          color: Colors.white,
          margin: EdgeInsets.only(top: 2),
          child: Column(children: <Widget>[
            SizedBox(
              height: 64, // 设置 ListView 的高度
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: pageData?.goodsPriceRanges?.length,
                itemBuilder: (context, index) {
                  return Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            onGetStepPrice(index),
                            style: Global.textWeight102,
                          ),
                          Text(onPriceRangeDesc(index),
                              style: Global.textSize106),
                        ],
                      ));
                },
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              child: Text(
                pageData?.goodsName ?? '',
                style: Global.textWeight101,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start, // 子组件从左到右排列
              children: <Widget>[
                Container(
                  padding:
                      EdgeInsets.only(left: 6, right: 6, top: 2, bottom: 6),
                  child: Text(
                    '${I18n.of(context)?.translate("cm_goods.goodsNo")}:',
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(left: 2),
                  child: Text(
                    pageData?.goodsNo ?? '',
                  ),
                )
              ],
            ),
          ])),
    );
  }

  Widget _buildProductSpec() {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            isDismissible: true, // 点击蒙层关闭
            enableDrag: false, // 拖动关闭
            builder: (BuildContext context) {
              return SizedBox(
                height: screenHeight * 0.8,
                child: GoodsSpecDrawer(
                  context: context,
                  goodsId: pageData?.id ?? '',
                  selectEvent: (int index) {},
                  onCartUpdate: (String goodsId) {
                    onCartData();
                  },
                  onGoodsSpecClose: () {
                    Navigator.pop(context);
                  },
                ),
              );
            });
      },
      splashColor: Colors.blue.withOpacity(0.5.sp),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(2),
        child: Container(
            margin: EdgeInsets.only(top: 2),
            padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 12.sp),
            decoration: BoxDecoration(
              color: Colors.white, // 白色背景

              borderRadius: BorderRadius.circular(8.0.sp), // 圆角
            ),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 9,
                    child: Text.rich(TextSpan(children: [
                      TextSpan(
                        text: I18n.of(context)
                                ?.translate("cm_goods.specification") ??
                            '',
                        style: Global.textColor105,
                      ),
                      TextSpan(
                        text: ' ${onSpecName()}',
                      ),
                      TextSpan(
                        text:
                            ' ${I18n.of(context)?.translate("cm_goods.optional")}',
                      )
                    ])),
                  ),
                  Expanded(child: Icon(Icons.arrow_right))
                ])),
      ),
    );
  }

  // 商品属性
  Widget _buildProductAttr() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.sp),
      child: Container(
        color: Colors.white,
        margin: EdgeInsets.only(top: 2),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(10),
              child: InkWell(
                onTap: () {
                  showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      isDismissible: true, // 点击蒙层关闭
                      enableDrag: false, // 拖动关闭
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(4), // 自定义顶部圆角半径（例如 24px）
                        ),
                      ),
                      builder: (BuildContext context) {
                        return Container(
                          height: screenHeight * 0.8,
                          padding: EdgeInsets.zero, // 关键：移除所有内边距
                          child: Column(
                            crossAxisAlignment:
                                CrossAxisAlignment.start, // 高度由内容决定
                            children: [
                              // 顶部内容
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10.sp, vertical: 10.sp),
                                child: Text(
                                    I18n.of(context)?.translate(
                                            "cm_goods.keyAributes") ??
                                        "",
                                    style: Global.textWeight101),
                              ),
                              // 中间滚动区域
                              Expanded(
                                child: SingleChildScrollView(
                                  child: ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: pageData?.attrList?.length,
                                      itemBuilder: (context, index) {
                                        Color backgroundColor = index % 2 == 0
                                            ? Colors.grey.shade100
                                            : Colors.white;
                                        return Container(
                                          color: backgroundColor,
                                          padding: const EdgeInsets.all(6),
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  pageData?.attrList?[index]
                                                          .attrName ??
                                                      '',
                                                  textAlign: TextAlign.left,
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  pageData?.attrList?[index]
                                                          .attrValue ??
                                                      '',
                                                  textAlign: TextAlign.left,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }),
                                ),
                              ),

                              Container(
                                width: double.infinity, // 关键：确保按钮水平铺满
                                padding: EdgeInsets.zero, // 关键：移除所有内边距

                                child: FancyButton(
                                    onTap: () {
                                      Navigator.pop(context); // 点击关闭抽屉
                                    },
                                    color: AppColors.primaryColor,
                                    borderColor: Colors.grey.shade300,
                                    padding:
                                        EdgeInsets.symmetric(vertical: 10.sp),
                                    borderRadius: BorderRadius.circular(16.0),
                                    child: Text(
                                        I18n.of(context)
                                                ?.translate("cm_goods.close") ??
                                            "",
                                        style: Global.textSize101)),
                              ),
                            ],
                          ),
                        );
                      });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 9,
                      child: Text(
                        I18n.of(context)?.translate("cm_goods.keyAributes") ??
                            '',
                        style: Global.textWeight102,
                      ),
                    ),
                    Expanded(child: Icon(Icons.arrow_right))
                  ],
                ),
              ),
            ),
            SizedBox(
              child: ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: (pageData?.attrList ?? []).length > 5
                      ? 5
                      : pageData?.attrList?.length,
                  itemBuilder: (context, index) {
                    Color backgroundColor =
                        index % 2 == 0 ? Colors.grey.shade100 : Colors.white;
                    return Container(
                      color: backgroundColor,
                      padding: const EdgeInsets.all(6),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 1,
                            child: Text(
                              pageData?.attrList?[index].attrName ?? '',
                              textAlign: TextAlign.left,
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              pageData?.attrList?[index].attrValue ?? '',
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  // 商品详情
  Widget _buildProductDetail() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Container(
        color: Colors.white,
        margin: EdgeInsets.only(top: 2),
        child: Column(
          children: [
            SizedBox(height: 5),
            Row(
                mainAxisAlignment: MainAxisAlignment.start, // 子组件从左到右排列
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.only(left: 6),
                    child: Text(
                      I18n.of(context)
                              ?.translate("cm_goods.productDescription") ??
                          '',
                      style: Global.textSize103,
                    ),
                  ),
                ]),
            SizedBox(height: 5),
            HtmlWidget(pageData?.goodsDesc ?? '')
          ],
        ),
      ),
    );
  }

  // 底部
  Widget _buildFooterBar() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              flex: 2,
              child: Center(
                child: Container(
                  padding: EdgeInsets.only(left: 2, top: 2),
                  child: GestureDetector(
                      onTap: () {
                        NavigatorUtil.pushNamed(
                            context, AppRoutes.CategoryPage);
                      },
                      child: Column(
                        children: [
                          Icon(
                            size: 24,
                            Icons.category_outlined,
                            color: Colors.grey.shade600,
                          ),
                          Text(
                            I18n.of(context)!.translate("cm_bar.category"),
                            style: Global.textColor103,
                          )
                        ],
                      )),
                ),
              )),
          Expanded(
            flex: 2,
            child: Center(
              child: Container(
                padding: EdgeInsets.only(left: 2, top: 2),
                child: GestureDetector(
                  onTap: () {
                    onGotoCartList(context);
                  },
                  child: Center(
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Column(
                          children: [
                            Icon(
                              size: 24,
                              Icons.shopping_cart_outlined,
                              color: Colors.grey.shade600,
                            ),
                            Text(
                              I18n.of(context)!.translate("cm_bar.list"),
                              style: Global.textSize104,
                            )
                          ],
                        ),
                        Visibility(
                          visible: (cartData?.stat?.goodsCount ?? 0) > 0,
                          child: Positioned(
                            right: -4,
                            top: 0,
                            child: Container(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 6),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              constraints: const BoxConstraints(
                                minHeight: 12,
                              ),
                              child: Text(
                                '${cartData?.stat?.goodsCount ?? 0}',
                                style: Global.textColor104,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
              flex: 5,
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: Center(
                  child: GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            isDismissible: true, // 点击蒙层关闭
                            enableDrag: false, // 拖动关闭
                            builder: (BuildContext context) {
                              return SizedBox(
                                height: screenHeight * 0.8,
                                child: GoodsSpecDrawer(
                                  context: context,
                                  goodsId: pageData?.id ?? '',
                                  selectEvent: (int index) {},
                                  onCartUpdate: (String goodsId) {
                                    onCartData();
                                  },
                                  onGoodsSpecClose: () {
                                    Navigator.pop(context);
                                  },
                                ),
                              );
                            });
                      },
                      child: Text(
                        I18n.of(context)?.translate("cm_find_addCart") ?? "",
                        style: Global.textSize103,
                      )),
                ),
              ))
        ],
      ),
    );
  }
}
