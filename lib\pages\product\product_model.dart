import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/models/common.dart';

class PageData {
  String? id;
  String? goodsNo;
  String? goodsName;
  String? goodsDesc;
  int? minBuyQuantity;
  int? minIncreaseQuantity;
  String? goodsPriceUnitName;
  String? videoUrl;
  List<AttrItem>? attrList;
  List<SkuItem>? skuList;
  List<SpecData> specList;
  List<String>? goodsImageList;
  List<GoodsPriceRange>? goodsPriceRanges;
  String? errorMessage;

  PageData(
      {this.id,
      this.goodsNo,
      this.goodsName,
      this.goodsDesc,
      this.skuList,
      required this.specList,
      this.minIncreaseQuantity,
      this.goodsPriceUnitName,
      this.videoUrl,
      this.goodsImageList,
      this.minBuyQuantity,
      this.goodsPriceRanges,
      this.attrList,
      this.errorMessage});

  factory PageData.fromJson(dynamic json) {
    return PageData(
      id: json?['id'] as String?,
      goodsDesc: json?['goodsDesc'] as String?,
      goodsNo: json?['goodsNo'] as String?,
      goodsName: json?['goodsName'] as String,
      minBuyQuantity: json?['minBuyQuantity'] as int?,
      goodsPriceUnitName: json?['goodsPriceUnitName'] as String,
      minIncreaseQuantity: json?['minIncreaseQuantity'] as int?,
      videoUrl: json?['videoUrl'] as String?,
      goodsImageList: (json?['goodsImageList'] as List<dynamic>?)
          ?.map((item) => item as String)
          .toList(),
      skuList: (json?['skuList'] as List<dynamic>?)
              ?.map((item) => SkuItem.fromJson(item))
              .toList() ??
          [],
      specList: (json?['specList'] as List<dynamic>?)
              ?.map((item) => SpecData.fromJson(item))
              .toList() ??
          [],
      goodsPriceRanges: (json?['goodsPriceRanges'] as List<dynamic>?)
              ?.map((item) => GoodsPriceRange.fromJson(item))
              .toList() ??
          [],
      attrList: (json?['attrList'] as List<dynamic>?)
              ?.map((item) => AttrItem.fromJson(item))
              .toList() ??
          [],
    );
  }
}

class ProductSpec {
  String? name;
  String? specId;
  String? itemId;
  String? itemName;
  bool? disabled;
  int? stockQty; //库存数量
  int? orderQty; //下单数量

  ProductSpec();
}

class SpecData {
  String? id;
  String name;
  List<SpecItem> items;

  SpecData({this.id, required this.name, required this.items});

  factory SpecData.fromJson(dynamic json) {
    return SpecData(
      id: json?['id'],
      name: json?['name'],
      items: (json?['items'] as List<dynamic>?)
              ?.map((item) => SpecItem.fromJson(item))
              .toList() ??
          [],
    );
  }
}

class SpecItem {
  String? itemId;
  String? itemName;
  int? cartQty;
  String? imageUrl;
  int? stockQty;
  int? orderQty;
  bool? selected;

  SpecItem(
      {this.itemId,
      this.itemName,
      this.cartQty,
      this.imageUrl,
      this.stockQty,
      this.selected,
      this.orderQty});

  factory SpecItem.fromJson(dynamic json) {
    return SpecItem(
        itemId: json?['itemId'],
        itemName: json?['itemName'],
        cartQty: json?['cartQty'] as int,
        imageUrl: json?['imageUrl'],
        stockQty: json?['stockQty'] as int,
        orderQty: 0,
        selected: false);
  }
}

class SkuData {
  List<SkuItem>? skuList;

  SkuData({this.skuList});

  factory SkuData.fromJson(dynamic json) {
    return SkuData(
      skuList: (json?['skuList'] as List<dynamic>?)
              ?.map((item) => SkuItem.fromJson(item))
              .toList() ??
          [],
    );
  }
}

class SkuItem {
  String? id;
  int cartQty;
  double price;
  String? skuNo;
  int? stockQty;
  String? skuName;
  List<SkuSpecItem> specItems;
  List<SkuStepPrice>? stepPrices;

  SkuItem(
      {this.id,
      this.skuNo,
      required this.cartQty,
      this.stockQty,
      required this.price,
      this.skuName,
      required this.specItems,
      this.stepPrices});

  factory SkuItem.fromJson(dynamic json) {
    List<SkuSpecItem> specItems = (json?['specItems'] as List<dynamic>?)
            ?.map((item) => SkuSpecItem.fromJson(item))
            .toList() ??
        [];

    List<SkuStepPrice> stepPrices = (json?['stepPrices'] as List<dynamic>?)
            ?.map((item) => SkuStepPrice.fromJson(item))
            .toList() ??
        [];

    return SkuItem(
      id: json?['id'],
      skuNo: json?['skuNo'],
      cartQty: json?['cartQty'] ?? 0,
      stockQty: json?['stockQty'],
      price: stepPrices.isNotEmpty ? (stepPrices[0].price ?? 0) : 0,
      specItems: specItems,
      skuName: specItems.map((item) => item.itemName).join(', '),
      stepPrices: stepPrices,
    );
  }
}

class SkuSpecItem {
  String? itemId; //规格id
  String? itemName; //规格名称
  String? specId; //规格项id

  SkuSpecItem({
    this.itemId,
    this.itemName,
    this.specId,
  });

  factory SkuSpecItem.fromJson(dynamic json) {
    return SkuSpecItem(
        itemId: json?['itemId'],
        itemName: json?['itemName'],
        specId: json?['specId']);
  }
}

class SelectSpecItem {
  String specName;
  String specItemName;
  String specItemId;
  int orderQty;

  SelectSpecItem(
      {required this.specName,
      required this.specItemName,
      required this.specItemId,
      this.orderQty = 0});
}

class AttrItem {
  String? attrId;
  String? attrName;
  String? attrValue;
  String? attributeItemType;
  List<String>? values;

  AttrItem(
      {this.attrId,
      this.attrName,
      this.attrValue,
      this.attributeItemType,
      this.values});

  factory AttrItem.fromJson(dynamic json) {
    List<String> values = [];
    if (json?['values'] != null) {
      json?['values'].forEach((v) {
        values.add(v);
      });
    }

    return AttrItem(
      attrId: json?['attrId'],
      attrName: json?['attrName'],
      attrValue: json?['attrValue'],
      attributeItemType: json?['attributeItemType'],
      values: values,
    );
  }
}

class GoodsListQueryParam {
  String? cateName;
  String? categoryId;
  String? keyword;
  String? imageId;
  String? imageUrl;
  List<String> tagIds;
  String? childCategoryId;
  String? marketingRuleId;
  String? padc;
  PageInfo? pageInfo;
  bool? loadingPage;
  bool? noMoreGoods;
  List<String> activeQueries;
  bool? categoryDropdown;
  bool? showMoreCate; //展示二级分类
  int? sortField;

  GoodsListQueryParam({
    this.cateName,
    this.categoryId,
    this.keyword,
    this.imageId,
    this.imageUrl = "",
    required this.tagIds,
    this.childCategoryId,
    this.marketingRuleId,
    this.padc,
    this.pageInfo,
    this.loadingPage,
    required this.activeQueries,
    this.categoryDropdown = true,
    this.showMoreCate,
    this.sortField,
    this.noMoreGoods = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'keyword': keyword,
      'categoryId': categoryId,
      'pageNo': pageInfo?.current ?? 1,
      'pageSize': pageInfo?.size ?? 30,
      'childCategoryId': childCategoryId,
      'imageId': imageId,
      'tagIds': tagIds,
      'marketingRuleId': marketingRuleId,
      'sortField': sortField,
      "padc": padc,
    };
  }
}

class ProductListQueryItem {
  String? key;
  String? value;
  bool selected;

  ProductListQueryItem({
    this.key,
    this.value,
    required this.selected,
  });
}

class ProductListQueryData {}
