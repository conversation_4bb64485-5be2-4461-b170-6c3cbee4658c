import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/image_search.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/product/components/category_widget.dart';
import 'package:chilat2_mall_app/pages/product/components/goods_spec.dart';
import 'package:chilat2_mall_app/pages/product/product_model.dart';
import 'package:chilat2_mall_app/pages/search/looking.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:marquee/marquee.dart';

class ProductList extends StatefulWidget {
  final String? type;
  final String? tagId;
  final String? cateName;
  final String? categoryId;
  final String? keyword;
  final String? imageId;
  final String? imageUrl;
  final String? childCategoryId;
  final String? padc;

  const ProductList({
    super.key,
    this.keyword,
    this.tagId,
    this.cateName,
    this.categoryId,
    this.type,
    this.imageId,
    this.imageUrl,
    this.childCategoryId,
    this.padc,
  });

  @override
  State<ProductList> createState() => _ProductListState();
}

class _ProductListState extends State<ProductList> {
  bool _isLoading = true;
  bool _isPinned = false;
  double screenWidth = 0.0;
  bool _goodsCardShow = false;
  OverlayEntry? _overlayEntry;
  final GlobalKey _tabKey = GlobalKey();
  int selectedCateIndex = 0;
  GoodsListPageModel pageData = GoodsListPageModel();
  List<ProductListQueryItem> queryItems = [];
  List<GoodsListCategoryFilterModel> filterCates = [];
  final GoodsListQueryParam _queryForm =
      GoodsListQueryParam(tagIds: [], activeQueries: []);
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();
  // final MainController mainController = Get.find<MainController>();

  final double _goodsWidth = 160.0; // 稍微增加卡片宽度

  @override
  void initState() {
    super.initState();

    _queryForm.keyword = Get.arguments?['keyword'] ?? '';
    _textController.text = Get.arguments?['keyword'] ?? '';
    _queryForm.cateName = Get.arguments?['cateName'] ?? '';
    _queryForm.categoryId = Get.arguments?['categoryId'] ?? '';
    _queryForm.imageId = Get.arguments?['imageId'] ?? '';
    _queryForm.childCategoryId = Get.arguments?['childCategoryId'] ?? '';
    _queryForm.imageUrl = Get.arguments?['imageUrl'] ?? '';
    _queryForm.categoryDropdown = true;
    _queryForm.showMoreCate = true;
    if (Get.arguments?['tagId'] != null) {
      _queryForm.tagIds = [Get.arguments?['tagId'] ?? ''];
    } else if (Get.arguments?['type'] != null) {
      _queryForm.tagIds = ['10000100', '10000200'];
    } else {
      _queryForm.tagIds = [];
    }

    onPageData();

    _scrollController.addListener(_checkScrollPosition);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 查询列表页数据
  Future<void> onPageData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      dynamic res = await ProductAPI.useGoodsListPage(_queryForm.toJson());
      if (res != null && res?['result']?['code'] == 200) {
        setState(() {
          pageData = GoodsListPageModel.fromJson(res['data']);
          final goodsList = pageData.pageData?.goodsList;
          if (goodsList != null && goodsList.isEmpty) {
            Navigator.push(
              Get.context!,
              MaterialPageRoute(
                builder: (ctx) => SearchLooking(
                  keyword: _queryForm.keyword,
                  cateName: _queryForm.cateName,
                  imageUrl: _queryForm.imageUrl,
                ),
              ),
            );
          }

          queryItems.add(ProductListQueryItem(
              key: 'addToCartCount',
              value: I18n.of(context)?.translate("cm_goods.sortByPopular"),
              selected: false));

          // Global.setCartList(data: res?['data']?['cartInfo']);
          // mainController.onCartDataUpdate();

          _queryForm.pageInfo = pageData.pageData?.page;

          pageData.pageData?.goodsList =
              pageData.pageData?.goodsList!.map((goods) {
            bool? exists = pageData.cartInfo?.goodsList
                .any((item) => item.goodsId == goods.goodsId);

            goods.selected = exists ?? false;
            return goods;
          }).toList();
          onGetMarketCate();
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 搜索商品数据
  Future<void> onGetGoodsListData({bool? scroll}) async {
    try {
      if (scroll == true && _queryForm.noMoreGoods == true) {
        return;
      }
      if (scroll == true) {
        _queryForm.pageInfo?.current = (_queryForm.pageInfo?.current ?? 1) + 1;
      } else {
        _queryForm.loadingPage = true;
        _queryForm.noMoreGoods = false;
        _queryForm.pageInfo?.current = 1;
      }
      if (_queryForm.activeQueries.contains("addToCartCount")) {
        _queryForm.sortField = 42;
      } else {
        _queryForm.sortField = null;
      }

      dynamic res = await ProductAPI.useGoodsPageListData(_queryForm.toJson());
      if (res != null && res?['result']?['code'] == 200) {
        setState(() {
          GoodsListDataModel data = GoodsListDataModel.fromJson(res?['data']);
          if (scroll == true) {
            pageData.pageData!.goodsList?.addAll(data.goodsList ?? []);
          } else {
            pageData.pageData!.goodsList = data.goodsList;
          }
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  //
  Future<void> onGetMarketCate() async {
    try {
      filterCates =
          pageData.pageData?.categoryFilters?.map((item) => item).toList() ??
              [];

      filterCates.insert(
          0,
          GoodsListCategoryFilterModel(
              id: "0",
              name: I18n.of(context)?.translate("cm_goods.all"),
              children: pageData.pageData?.categoryFilters
                      ?.map((item) => item)
                      .toList() ??
                  []));
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 计算滚动位置
  void _checkScrollPosition() {
    final double threshold = 100; // 距离底部 100 像素时触发
    final position = _scrollController.position;
    final currentOffset = _scrollController.offset;
    if (position.pixels >= position.maxScrollExtent - threshold) {
      // 执行加载更多操作
      onGetGoodsListData(scroll: true);
    }

    final shouldPin = currentOffset >= 200;
    if (shouldPin != _isPinned) {
      setState(() => _isPinned = shouldPin);
    }
  }

  // 显示下拉抽屉
  void _showDropdown(BuildContext context, int index) async {
    // 关闭已存在的抽屉
    _hideDropdown();

    // 获取标签位置信息
    final RenderBox renderBox =
        _tabKey.currentContext?.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // 创建覆盖层
    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: _hideDropdown,
        behavior: HitTestBehavior.translucent,
        child: Material(
          color: Colors.transparent,
          child: Stack(
            children: [
              // 半透明遮罩层
              Positioned.fill(
                top: position.dy + renderBox.size.height,
                child: Container(
                  color: Colors.black.withOpacity(0.2),
                ),
              ),
              Positioned(
                // left: position.dx,
                top: position.dy + renderBox.size.height,
                child: _buildDropdownContent(index),
              ),
            ],
          ),
        ),
      ),
    );

    // 插入覆盖层
    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隐藏抽屉
  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // 构建抽屉内容
  Widget _buildDropdownContent(int index) {
    final crossAxisCount =
        (MediaQuery.sizeOf(context).width / _goodsWidth).floor();

    return Container(
      height: _goodsWidth,
      color: Colors.white,
      width: MediaQuery.sizeOf(context).width,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: GridView.builder(
        padding: EdgeInsets.symmetric(horizontal: 4, vertical: 6),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 6,
          mainAxisSpacing: 6,
          childAspectRatio: 6.5, // 根据文本内容调整宽高比
        ),
        itemCount: filterCates[index].children?.length,
        itemBuilder: (context, childrenIdx) {
          return GestureDetector(
            onTap: () {
              setState(() {
                if (index == 0) {
                  _queryForm.childCategoryId = null;
                  _queryForm.categoryId = filterCates[index].id;
                  _queryForm.categoryDropdown = true;
                  onGetGoodsListData();
                } else {
                  _queryForm.childCategoryId =
                      filterCates[index].children?[childrenIdx].id;

                  _hideDropdown();
                  _queryForm.categoryDropdown =
                      _queryForm.categoryDropdown == true ? false : true;

                  // Navigator.push(
                  //   Get.context!,
                  //   MaterialPageRoute(
                  //     builder: (ctx) => ProductList(
                  //       categoryId: _queryForm.categoryId,
                  //       childCategoryId: _queryForm.childCategoryId,
                  //       type: Get.arguments?['type'] == "recommendSearch"
                  //           ? "recommendSearch"
                  //           : null,
                  //       tagId: Get.arguments?['tagId'] ?? '',
                  //     ),
                  //   ),
                  // );
                  NavigatorUtil.pushNamed(context, AppRoutes.ProductList,
                      arguments: {
                        "categoryId": _queryForm.categoryId,
                        "childCategoryId": _queryForm.childCategoryId,
                        "type": Get.arguments?['type'] == "recommendSearch"
                            ? "recommendSearch"
                            : null,
                        "tagId": Get.arguments?['tagId'] ?? '',
                      });
                }
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                filterCates[index].children?[childrenIdx].name ?? "",
                style: TextStyle(fontSize: 12, color: Colors.black),
                textAlign: TextAlign.start,
                softWrap: true,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> onCartList() async {
    try {
      MidCartModel? cartData = await Global.getCartData();

      setState(() {
        pageData.pageData?.goodsList =
            pageData.pageData?.goodsList?.map((goods) {
          goods.selected = (cartData?.goodsList ?? [])
              .any((item) => item.goodsId == goods.goodsId);
          return goods;
        }).toList();
      });
      // }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 活动标签事件
  Future<void> onActiveQueryEvent(ProductListQueryItem query) async {
    bool exists = _queryForm.activeQueries.contains(query.key);

    if (exists) {
      _queryForm.activeQueries.remove(query.key);
    } else if (query.key != null) {
      _queryForm.activeQueries.add(query.key ?? '');
    }
    onGetGoodsListData();
  }

  Future<String?> onGetGoodsId(String sourceGoodsId) async {
    try {
      dynamic res = await ProductAPI.useGetGoods({
        "str": sourceGoodsId,
      });
      if (res != null && res?['result']?['code'] == 200) {
        return res['data'];
      } else {
        showErrorMessage(
            I18n.of(context)!.translate("cm_common_addGoodsError"));
        return null;
      }
    } catch (e) {
      showErrorMessage(e.toString());
      return null;
    }
  }

  Future<void> onGotoProductDetail(GoodsListDataItemModel? goods) async {
    try {
      if (goods?.goodsId?.isEmpty ?? true) {
        goods?.goodsId = await onGetGoodsId(goods.sourceGoodsId ?? "");
        if (goods?.goodsId?.isEmpty ?? true) {
          return;
        }
      }

      Get.toNamed(AppRoutes.ProductPage,
          arguments: {'productId': goods?.goodsId ?? ""});
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onAddToCart(BuildContext context, GoodsListDataItemModel? goods,
      double screenHeight) async {
    try {
      if (goods?.goodsId?.isEmpty ?? true) {
        goods?.goodsId = await onGetGoodsId(goods.sourceGoodsId ?? "");
        if (goods?.goodsId?.isEmpty ?? true) {
          return;
        }
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.sizeOf(context).width;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            showScrollToTopButton: true,
            backgroundColor: Colors.white,
            scrollController: _scrollController,
            scrollToTopThreshold: 200,
            body: Column(
              children: [
                _buildTopSection(),
                Expanded(child: _buildGoodsList()),
                Visibility(
                    visible: _queryForm.noMoreGoods == true,
                    child: Text(
                      I18n.of(context)!.translate("cm_goods.noMoreGoods"),
                      softWrap: true,
                      maxLines: 2,
                    ))
              ],
            ),
          );
  }

  Widget _buildTopSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: _isPinned == false,
            child: _buildSearchBar(),
          ),
          Visibility(
            visible: _queryForm.imageUrl?.isEmpty ?? true,
            child: CategoryWidget(
              screenWidth: screenWidth,
              categoryId: _queryForm.categoryId ?? '0',
              categories: filterCates,
              onCategoryChange: (isAll, categoryId, childCategoryId) {
                if (isAll) {
                  _queryForm.categoryId = categoryId;
                  _queryForm.childCategoryId = childCategoryId;
                  _queryForm.pageInfo?.current = 1;

                  onGetGoodsListData();
                } else {
                  NavigatorUtil.pushNamed(context, AppRoutes.ProductList,
                      arguments: {
                        "categoryId": categoryId,
                        "childCategoryId": childCategoryId,
                        "type": widget.type == "recommendSearch"
                            ? "recommendSearch"
                            : null,
                        "tagId": widget.tagId,
                      });
                }
              },
            ),
          ),
          Visibility(
              visible: _queryForm.imageUrl?.isEmpty ?? true,
              child: _buildHotSearch()),
          // 以图搜图
          Visibility(
              visible: _queryForm.imageUrl?.isNotEmpty ?? false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MyCachedNetworkImage(
                      imageurl: _queryForm.imageUrl ?? '',
                      width: 32.sp,
                      height: 32.sp,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }

  // 商品列表
  Widget _buildGoodsList() {
    screenWidth = MediaQuery.sizeOf(context).width;
    // 计算每行元素个数
    final crossAxisCount = (screenWidth / _goodsWidth).floor();

    return Visibility(
        visible: pageData.pageData?.goodsList?.length != null,
        child: GridView.builder(
          padding: EdgeInsets.all(2),
          controller: _scrollController,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _goodsCardShow ? crossAxisCount : 1,
            childAspectRatio: _goodsCardShow ? 0.72 : 4,
            mainAxisSpacing: _goodsCardShow ? 20 : 16,
            crossAxisSpacing: 12,
          ),
          itemCount: pageData.pageData?.goodsList?.length,
          itemBuilder: (context, index) {
            GoodsListDataItemModel? product =
                pageData.pageData?.goodsList?[index];

            return _goodsCardShow
                ? buildProductCard(product)
                : _buildProductItem(product);
          },
        ));
  }

  // 搜索栏
  Widget _buildSearchBar() {
    return Container(
        color: Colors.white,
        margin: EdgeInsets.symmetric(horizontal: 0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Container(
            height: 20,
            padding: EdgeInsets.symmetric(vertical: 0),
            child: Marquee(
              text: I18n.of(context)?.translate("cm_common.tip") ?? '',
              style: TextStyle(color: Colors.red.shade300, fontSize: 16),
              scrollAxis: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.start,
              blankSpace: 20.0,
              velocity: 100.0,
              pauseAfterRound: Duration(seconds: 1),
              startPadding: 10.0,
              accelerationDuration: Duration(seconds: 1),
              accelerationCurve: Curves.linear,
              decelerationDuration: Duration(milliseconds: 2000),
              decelerationCurve: Curves.easeOut,
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: 24,
                  margin: EdgeInsets.only(left: 8.sp),
                  child: GestureDetector(
                    onTap: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      } else {
                        NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.sp),
                      child: SvgPicture.asset(
                        'assets/images/common/arrow-left.svg',
                        width: 18.sp,
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 28,
                  color: Colors.white,
                  width: screenWidth * 0.68,
                  margin: EdgeInsets.only(left: 12.sp),
                  child: TextField(
                    controller: _textController,
                    onChanged: (value) {
                      _queryForm.keyword = value;
                    },
                    decoration: InputDecoration(
                        hintText: I18n.of(context)
                            ?.translate('cm_search.searchPlaceholder'),
                        hintStyle: TextStyle(fontSize: 12),
                        filled: true,
                        fillColor: Colors.grey.shade100,
                        contentPadding: EdgeInsets.symmetric(vertical: 2),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide.none,
                        ),
                        // 未聚焦时的边框
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.red, width: 0.5),
                        ),
                        // 聚焦时的边框
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: Colors.red, width: 1), // 聚焦时加粗
                        ),
                        prefixIcon: Icon(Icons.search, size: 24),
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              onGetGoodsListData();
                            });
                          },
                          child: Container(
                            width: 56,
                            margin: EdgeInsets.only(left: 4),
                            padding: EdgeInsets.symmetric(horizontal: 3),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                bottomLeft: Radius.circular(4),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                I18n.of(context)?.translate('cm_home.search') ??
                                    'Search',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        )),
                  ),
                ),
                const Spacer(), // 占据剩余空间
                Container(
                  padding: EdgeInsets.only(left: 6.sp),
                  child: ImageSearchWidget(
                    onImageSearch: (imageId, imageUrl) {
                      setState(() {
                        NavigatorUtil.pushNamed(context, AppRoutes.ProductList,
                            arguments: {
                              "imageId": imageId,
                              "imageUrl": imageUrl,
                              "type": "imgSearch",
                            });
                      });
                    },
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _goodsCardShow = !_goodsCardShow;
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 6.sp),
                    child: Icon(
                      _goodsCardShow == true
                          ? Icons.format_list_numbered
                          : Icons.grid_view,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          )
        ]));
  }

  // 热搜标签
  Widget _buildHotSearch() {
    return Wrap(
      spacing: 0, // 水平间距
      runSpacing: 0, // 垂直间距
      children: queryItems.map((item) {
        return Container(
          margin: EdgeInsets.only(top: 4, left: 4),
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            border: Border.all(
                color: item.selected ? Colors.red : Colors.transparent,
                width: 0),
            color: item.selected ? Colors.white : Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: GestureDetector(
            onTap: () {
              setState(() {
                item.selected = !item.selected;
                onActiveQueryEvent(item);
              });
            },
            child: Text(item.value ?? "",
                style: TextStyle(
                    color: item.selected ? Colors.red : Colors.black87)),
          ),
        );
      }).toList(),
    );
  }

  Widget buildProductCard(GoodsListDataItemModel? goods) {
    dynamic price = goods?.minPrice != goods?.maxPrice
        ? '${setUnit(goods?.minPrice ?? 0)}-${goods?.maxPrice}'
        : setUnit(goods?.maxPrice);

    return GestureDetector(
      onTap: () {
        // Get.toNamed(AppRoutes.ProductPage,
        //     arguments: {'productId': goods?.goodsId});
        onGotoProductDetail(goods);
      },
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: ClipRRect(
                borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                child: MyCachedNetworkImage(
                  imageurl: goods?.mainImageUrl ?? '',
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(height: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  goods?.goodsName ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600],
                    height: 1.2,
                  ),
                ),
                SizedBox(height: 10),
                // 商品价格
                Text(
                  price.toString(),
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductItem(GoodsListDataItemModel? goods) {
    final screenHeight = MediaQuery.of(context).size.height;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2, vertical: 2),
      child: InkWell(
          onTap: () {
            onGotoProductDetail(goods);
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(color: Colors.grey.withAlpha(2), blurRadius: 4)
                ],
              ),
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                MyCachedNetworkImage(
                  imageurl: goods?.mainImageUrl ?? '',
                  width: 100,
                  height: 100,
                  fit: BoxFit.cover, // 添加适配方式
                  borderRadius: BorderRadius.circular(4),
                ),
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: EdgeInsets.only(left: 2, top: 2),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 2),
                          child: Text(
                            '${goods?.goodsName}',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.only(left: 2, top: 2),
                          child: Text.rich(TextSpan(children: [
                            TextSpan(
                              text: setUnit(goods?.minPrice),
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  // color: AppColors.primaryColor,
                                  fontWeight: FontWeight.w600),
                            ),
                            // TextSpan(
                            //     text: '/ ${goods?.goodsPriceUnitName}',
                            //     style: TextStyle(
                            //         color: Colors.grey.shade500,
                            //         fontSize: 12,
                            //         fontWeight: FontWeight.w400))
                          ])),
                        ),
                        Visibility(
                          visible: goods?.pcsEstimateFreight != null,
                          child: Container(
                            padding: EdgeInsets.only(left: 2),
                            child: Text(
                              '${I18n.of(context)?.translate('cm_goods.shippingCost')}${setUnit(goods?.pcsEstimateFreight)}/ ${goods?.goodsPriceUnitName}',
                              style: TextStyle(
                                  fontSize: 12, fontWeight: FontWeight.w500),
                            ),
                          ),
                        ),
                        // 购物车按钮（独立手势区域）
                        Container(
                          margin: EdgeInsets.only(right: 12, bottom: 2),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                flex: 4,
                                child: Container(
                                  color: Colors.white.withAlpha(2),
                                  child: const Text(''),
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: GestureDetector(
                                  behavior: HitTestBehavior.opaque, // 关键：拦截事件冒泡
                                  onTap: () {
                                    showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        isDismissible: true, // 点击蒙层关闭
                                        enableDrag: false, // 拖动关闭
                                        builder: (BuildContext context) {
                                          return SizedBox(
                                            height: screenHeight * 0.8,
                                            child: GoodsSpecDrawer(
                                              context: context,
                                              goodsId: goods?.goodsId ?? '',
                                              sourceGoodsId:
                                                  goods?.sourceGoodsId ?? '',
                                              selectEvent: (int index) {},
                                              onCartUpdate: (String goodsId) {
                                                setState(() {
                                                  onCartList();
                                                });
                                              },
                                              onGoodsSpecClose: () {
                                                Navigator.pop(context);
                                              },
                                            ),
                                          );
                                        });
                                  },
                                  child: Align(
                                    alignment: Alignment.centerRight,
                                    child: Icon(
                                      Icons.shopping_cart_outlined,
                                      color: goods?.selected == true
                                          ? Colors.red
                                          : Colors.grey,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ]))),
    );
  }
}
