// 普通文章
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_search_bar.dart';
import 'package:chilat2_mall_app/services/article.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';

class ArticlePage extends StatefulWidget {
  final String? title;
  final String? articleId;
  final String? articleCode;

  const ArticlePage({super.key, this.articleId, this.title, this.articleCode});

  @override
  State<ArticlePage> createState() => _ArticlePageState();
}

class _ArticlePageState extends State<ArticlePage> {
  bool _isLoading = true;
  final dynamic _pageData = {};
  bool _showBackToTopButton = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    onPageData();
    _scrollController.addListener(() {
      setState(() {
        _showBackToTopButton = _scrollController.offset > 200;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onScrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Future<void> onPageData([bool? scroll]) async {
    try {
      dynamic res = await ArticleAPI.useArticleDetail({
        'id': Get.arguments?['articleId'],
        'title': Get.arguments?['title'],
        'articleCode': Get.arguments?['articleCode'],
      });
      if (res?['result']?['code'] == 200) {
        _pageData['articleDetail'] = res?['data'];
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            body: Stack(children: [
              SearchHeader(
                showHomeIcon: true,
              ),
              Container(
                margin: EdgeInsets.only(top: 84),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: HtmlWidget(_pageData['articleDetail']?['content']),
                ),
              ),
              if (_showBackToTopButton)
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: FloatingActionButton(
                    onPressed: onScrollToTop,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(64),
                    ),
                    child: const Icon(Icons.arrow_upward),
                  ),
                ),
            ]),
          );
  }
}
