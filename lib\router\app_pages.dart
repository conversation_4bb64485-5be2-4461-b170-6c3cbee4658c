import 'package:chilat2_mall_app/pages/article/article_page.dart';
import 'package:chilat2_mall_app/pages/article/blog_page.dart';
import 'package:chilat2_mall_app/pages/article/commission.dart';
import 'package:chilat2_mall_app/pages/article/faq.dart';
import 'package:chilat2_mall_app/pages/article/frequently_questions.dart';
import 'package:chilat2_mall_app/pages/cart/cart_page.dart';
import 'package:chilat2_mall_app/pages/find/inquiry_submit.dart';
import 'package:chilat2_mall_app/pages/mine/mine_order_page.dart';
import 'package:chilat2_mall_app/pages/product/product_list.dart';
import 'package:chilat2_mall_app/pages/product/product_page.dart';
import 'package:chilat2_mall_app/pages/search/looking.dart';
import 'package:chilat2_mall_app/pages/splash/splash_page.dart';
import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/pages/article/about_us.dart';
import 'package:chilat2_mall_app/pages/article/invited_reward_page.dart';
import 'package:chilat2_mall_app/pages/article/help_center_page.dart';
import 'package:chilat2_mall_app/pages/article/quick_guide_page.dart';
import 'package:chilat2_mall_app/pages/article/payment_methods_page.dart';
import 'package:chilat2_mall_app/pages/category/category_page.dart';
import 'package:chilat2_mall_app/pages/find/submit_thankyou.dart';
import 'package:chilat2_mall_app/pages/home/<USER>';
import 'package:chilat2_mall_app/pages/login/login_page.dart';
import 'package:chilat2_mall_app/pages/login/update_password.dart';
import 'package:chilat2_mall_app/pages/login/modify_password_page.dart';
import 'package:chilat2_mall_app/pages/login/register_page.dart';
import 'package:chilat2_mall_app/pages/login/register_success_page.dart';
import 'package:chilat2_mall_app/pages/login/terms_page.dart';
import 'package:chilat2_mall_app/pages/mine/mine_page.dart';
import 'package:chilat2_mall_app/pages/mine/mine_inquiry_page.dart';
import 'package:chilat2_mall_app/pages/mine/mine_address_page.dart';
import 'package:chilat2_mall_app/pages/mine/mine_coupon_page.dart';
import 'package:chilat2_mall_app/pages/mine/mine_invite_page.dart';
import 'package:chilat2_mall_app/pages/mine/mine_setting_page.dart';
import 'package:chilat2_mall_app/pages/order/order_detail_page.dart';
import 'package:chilat2_mall_app/pages/order/order_payment_page.dart';
import 'package:chilat2_mall_app/pages/order/order_detail_binding.dart';
import 'package:chilat2_mall_app/pages/order/order_payment_result_page.dart';
import 'package:chilat2_mall_app/pages/notfound/notfound_view.dart';
import 'package:chilat2_mall_app/pages/common/auth_block_page.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/global.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';

part 'app_routes.dart';

// 使用模态登录验证中间件
class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    if (!Global.isLogin.value) {
      Future.microtask(() {
        final context = Get.context;
        if (context != null) {
          AuthHelper.showLoginModal(
            context,
            redirectRoute: route,
            onAuthSuccess: () {
              if (route != null) {
                Future.delayed(const Duration(milliseconds: 100), () {
                  Get.offNamed(route);
                });
              }
            },
          );
        }
      });

      // 阻止路由跳转，跳到一个空白等待页
      return const RouteSettings(name: AppRoutes.AuthBlockPage);
    }

    return null; // 已登录，允许跳转
  }
}

class AppPages {
  static final unknownRoute = GetPage(
    name: AppRoutes.NotFound,
    page: () => const NotfoundPage(),
  );

  static final routes = [
    GetPage(
      name: AppRoutes.SplashPage,
      page: () => SplashPage(),
      transition: Transition.fade,
    ),
    GetPage(
      name: AppRoutes.LoginPage,
      page: () => LoginPage(),
    ),
    GetPage(
      name: AppRoutes.ModifyPassword,
      page: () => ModifyPasswordPage(),
    ),
    GetPage(
      name: AppRoutes.Register,
      page: () => RegisterPage(),
    ),
    GetPage(
      name: AppRoutes.RegisterSuccess,
      page: () => const RegisterSuccessPage(),
    ),
    GetPage(
      name: AppRoutes.Terms,
      page: () => TermsPage(),
    ),
    GetPage(
      name: AppRoutes.UpdatePassword,
      page: () => const UpdatePasswordPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.HomePage,
      page: () => HomePage(),
    ),
    GetPage(
      name: AppRoutes.CategoryPage,
      page: () => CategoryPage(),
    ),
    GetPage(
      name: AppRoutes.CartPage,
      page: () => CartPage(),
      // middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.MinePage,
      page: () => MinePage(),
    ),
    GetPage(
      name: AppRoutes.MineOrderPage,
      page: () => const MineOrderPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.MineInquiryPage,
      page: () => MineInquiryPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.MineAddressPage,
      page: () => MineAddressPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.MineCouponPage,
      page: () => const MineCouponPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.MineInvitePage,
      page: () => MineInvitePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.OrderDetail,
      page: () => const OrderDetailPage(),
      binding: OrderDetailBinding(),
      transition: Transition.rightToLeft,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.OrderPayment,
      page: () => const OrderPaymentPage(),
      transition: Transition.rightToLeft,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.OrderPayResults,
      page: () => OrderPaymentResultPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: AppRoutes.MineSettingPage,
      middlewares: [AuthMiddleware()],
      page: () => MineSettingPage(),
    ),
    GetPage(name: AppRoutes.FindSubmitThankyou, page: () => SubmitThankyou()),
    GetPage(
        name: AppRoutes.FindSubmit,
        page: () => InquirySubmit(
              fromCart: null,
            )),
    GetPage(name: AppRoutes.SearchLooking, page: () => SearchLooking()),
    GetPage(
      name: AppRoutes.AboutUs,
      page: () => AboutUsPage(),
    ),
    GetPage(
      name: AppRoutes.ArticleCommission,
      page: () => CommissionPage(),
    ),
    GetPage(
      name: AppRoutes.ArticlePage,
      page: () => ArticlePage(),
    ),
    GetPage(
      name: AppRoutes.BlogPage,
      page: () => BlogPage(),
    ),
    GetPage(
      name: AppRoutes.ArticleFAQ,
      page: () => FAQPage(),
    ),
    GetPage(
      name: AppRoutes.FrequentlyQuestions,
      page: () => FrequentlyQuestionsPage(),
    ),
    GetPage(
      name: AppRoutes.InvitedReward,
      page: () => const InvitedRewardPage(),
    ),
    GetPage(
      name: AppRoutes.HelpCenter,
      page: () => const HelpCenterPage(),
    ),
    GetPage(
      name: AppRoutes.QuickGuide,
      page: () => QuickGuidePage(),
    ),
    GetPage(
      name: AppRoutes.PaymentMethods,
      page: () => PaymentMethodsPage(),
    ),
    GetPage(name: AppRoutes.ProductList, page: () => ProductList()),
    GetPage(name: AppRoutes.ProductPage, page: () => ProductPage()),
    GetPage(
      name: AppRoutes.AuthBlockPage,
      page: () => const AuthBlockPage(), // 空白等待页
    ),
  ];
}
