import 'dart:math';

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class LoadingWidget extends StatelessWidget {
  final String? showType;
  const LoadingWidget({super.key, this.showType});

  @override
  Widget build(BuildContext context) {
    // 根据不同类型返回不同样式的卡片
    switch (showType) {
      case "Shimmer":
        return ShimmerSkeletonWidget();
      case "Animated":
        return AnimatedSkeletonWidget();
      case "Linear":
        return LinearProgressWidget();
      default:
        return CircularProgressWidget();
    }
  }
}

// 环形进度条
class CircularProgressWidget extends StatelessWidget {
  const CircularProgressWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Transform.scale(
      scale: 3.5,
      child: CircularProgressIndicator(
        // value: 0.7, // 0.0-1.0, 不设置则为无限旋转
        backgroundColor: Colors.grey.shade200, // 背景色
        // valueColor: AlwaysStoppedAnimation<Color>(Colors.green.shade400), // 进度条颜色
        valueColor: AlwaysStoppedAnimation<Color>(
          Colors.primaries[
              (DateTime.now().second ~/ 3) % Colors.primaries.length],
        ),
        strokeWidth: 2.0, // 进度条宽度
      ),
    ));
  }
}

// 线性进度条
class LinearProgressWidget extends StatelessWidget {
  const LinearProgressWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
        child: LinearProgressIndicator(
      value: 0.7, // 可选：设置具体进度
      backgroundColor: Colors.grey.shade200,
      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
    ));
  }
}

// 具有流动动画效果的骨架屏组件
class ShimmerSkeletonWidget extends StatelessWidget {
  const ShimmerSkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.width;

    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
              height: screenHeight * 0.35,
              width: double.infinity,
              color: Colors.white),
          const SizedBox(height: 1),
          Container(
              height: screenHeight * 0.15,
              width: double.infinity,
              color: Colors.white),
          const SizedBox(height: 1),
          Container(
              height: screenHeight * 0.3,
              width: double.infinity,
              color: Colors.white),
          const SizedBox(height: 1),
          Container(
              height: screenHeight * 0.2,
              width: double.infinity,
              color: Colors.white),
          const SizedBox(height: 1),
          Container(
              height: screenHeight * 0.36,
              width: double.infinity,
              color: Colors.white),
          const SizedBox(height: 1),
          Container(
              height: screenHeight * 0.32,
              width: double.infinity,
              color: Colors.white),
          const SizedBox(height: 1),
          Container(
              height: screenHeight * 0.25,
              width: double.infinity,
              color: Colors.white),
        ],
      ),
    );
  }
}

class AnimatedSkeletonWidget extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadiusGeometry? borderRadius;

  const AnimatedSkeletonWidget({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity,
    this.borderRadius,
  });

  @override
  State<AnimatedSkeletonWidget> createState() => _AnimatedSkeletonWidgetState();
}

class _AnimatedSkeletonWidgetState extends State<AnimatedSkeletonWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.3, end: 0.7).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width ?? MediaQuery.of(context).size.width,
          height: widget.height ?? MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            color: Colors.grey[200]!.withOpacity(_animation.value),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
          ),
        );
      },
    );
  }
}

// 环形加载动画
class RotationalLoadingWidget extends StatefulWidget {
  final double size;
  final String logo;
  final Duration duration;
  final int dotCount;
  final Color dotColor;

  const RotationalLoadingWidget({
    super.key,
    this.size = 120.0,
    this.logo = 'assets/images/common/loading.gif',
    this.duration = const Duration(seconds: 2),
    this.dotCount = 12,
    this.dotColor = Colors.grey,
  });

  @override
  State<RotationalLoadingWidget> createState() =>
      _RotationalLoadingWidgetState();
}

class _RotationalLoadingWidgetState extends State<RotationalLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 旋转的点
          // RotationTransition(
          //   turns: _controller,
          //   child: CustomPaint(
          //     size: Size(widget.size, widget.size),
          //     painter: DotsPainter(
          //       dotCount: widget.dotCount,
          //       dotColor: widget.dotColor,
          //     ),
          //   ),
          // ),

          // 中间 Logo
          Container(
            width: widget.size * 0.65,
            height: widget.size * 0.65,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: Image.asset(widget.logo, fit: BoxFit.contain),
          ),
        ],
      ),
    );
  }
}

class DotsPainter extends CustomPainter {
  final int dotCount;
  final Color dotColor;

  DotsPainter({
    required this.dotCount,
    required this.dotColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width * 0.85) / 2; // 点的分布半径

    // 创建点的画笔
    final dotPaint = Paint()
      ..color = dotColor
      ..style = PaintingStyle.fill;

    // 绘制多个点
    for (int i = 0; i < dotCount; i++) {
      final angle = (2 * pi / dotCount) * i;
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      // 根据位置调整点的透明度，形成动画效果
      final opacity = 1.0 - (i / dotCount);
      dotPaint.color = dotColor.withOpacity(opacity);

      canvas.drawCircle(Offset(x, y), 3.0, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant DotsPainter oldDelegate) {
    return false;
  }
}
