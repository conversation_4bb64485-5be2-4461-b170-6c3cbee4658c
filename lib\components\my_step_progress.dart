import 'package:chilat2_mall_app/models/common.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';

class MyStepProgress extends StatefulWidget {
  final int currentStep;
  final Color activateColor; // 激活颜色
  final Color inactiveColor; // 未激活颜色
  final double lineThickness; // 线段粗细
  final double circleSize; // 圆形直径
  final TextStyle? textStyle; // 文字样式
  final List<StepItem>? steps;
  const MyStepProgress({
    super.key,
    required this.currentStep,
    this.activateColor = Colors.red,
    this.inactiveColor = Colors.grey,
    this.lineThickness = 1.0,
    this.circleSize = 16.0,
    this.textStyle,
    this.steps,
  });

  @override
  State<MyStepProgress> createState() => _MyStepProgressState();
}

class _MyStepProgressState extends State<MyStepProgress> {
  double screenWidth = 0.0;
  late List<StepItem> _steps;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    _steps = widget.steps ??
        [
          StepItem(
              title: '1',
              description: I18n.of(context)!.translate("cm_common.step1")),
          StepItem(
              title: '2',
              description: I18n.of(context)!.translate("cm_common.step2")),
          StepItem(
              title: '3',
              description: I18n.of(context)!.translate("cm_common.step3")),
        ];

    return Container(
      height: 58,
      width: screenWidth,
      margin: EdgeInsets.only(top: 12, bottom: 2, left: 2, right: 2),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _steps.length,
        itemBuilder: (context, index) {
          dynamic step = _steps[index];
          return _stepProgressWidget(step, index == widget.currentStep);
        },
      ),
    );
  }

  Widget _stepProgressWidget(StepItem step, bool selected) {
    return Container(
      width: screenWidth / _steps.length,
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Column(
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            Expanded(
              child: Container(
                height: 2.5,
                color: selected ? widget.activateColor : widget.inactiveColor,
              ),
            ),
            Container(
              width: widget.circleSize,
              height: widget.circleSize,
              decoration: BoxDecoration(
                color: selected ? widget.activateColor : widget.inactiveColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  step.title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                height: 2.5,
                color: selected ? widget.activateColor : widget.inactiveColor,
              ),
            ),
          ]),
          Container(
            width: screenWidth / _steps.length,
            padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
            child: Center(
              child: Text(
                step.description,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(fontSize: 12),
              ),
            ),
          )
        ],
      ),
    );
  }
}
