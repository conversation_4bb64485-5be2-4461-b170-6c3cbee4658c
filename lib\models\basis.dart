import 'package:chilat2_mall_app/models/common.dart';

class MidCartModel {
  bool? selectAll;
  MidCartStatModel? stat;
  InquiryModel? lastInquiry;
  double? totalEstimateFreight; //预估运费总计（全部SKU可预估运费时，才返回）
  double? partEstimateFreight; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int? freightGoodsQty; //参与预估运费的商品数量
  List<MidCartGoodsModel> goodsList = [];

  MidCartModel(
      {this.selectAll,
      this.stat,
      required this.goodsList,
      this.lastInquiry,
      this.totalEstimateFreight,
      this.partEstimateFreight,
      this.freightGoodsQty});

  factory MidCartModel.fromJson(dynamic json) {
    List<MidCartGoodsModel> goodsList = (json?['goodsList'] as List<dynamic>?)
            ?.map((item) => MidCartGoodsModel.fromJson(item))
            .toList() ??
        [];

    return MidCartModel(
      selectAll: false,
      stat: MidCartStatModel.fromJson(json?['stat']),
      goodsList: goodsList,
      lastInquiry: InquiryModel.fromJson(json?['lastInquiry']),
      totalEstimateFreight: json?['totalEstimateFreight'] as double?,
      partEstimateFreight: json?['partEstimateFreight'] as double?,
      freightGoodsQty: json?['freightGoodsQty'] as int?,
    );
  }
}

class MidCartStatModel {
  int? onlineSkuCount;
  int? selectSkuCount;
  int goodsCount;
  int? skuCount;
  double? totalSalePrice;
  int? selectSkuTotalQuantity;
  double? selectTotalSalePrice;

  MidCartStatModel({
    this.onlineSkuCount,
    this.selectSkuCount,
    this.goodsCount = 0,
    this.skuCount,
    this.totalSalePrice,
    this.selectSkuTotalQuantity,
    this.selectTotalSalePrice,
  });

  factory MidCartStatModel.fromJson(dynamic json) {
    return MidCartStatModel(
      onlineSkuCount: json?['onlineSkuCount'] as int?,
      selectSkuCount: json?['selectSkuCount'] as int?,
      goodsCount: json?['goodsCount'] as int? ?? 0,
      skuCount: json?['skuCount'] as int?,
      totalSalePrice: json?['totalSalePrice'] as double?,
      selectSkuTotalQuantity: json?['selectSkuTotalQuantity'] as int?,
      selectTotalSalePrice: json?['selectTotalSalePrice'] as double?,
    );
  }
}

class GoodsExtendItem {
  String? groupId; // 字典ID
  String? groupName; // 字典名称（例：商品规格标题；商品属性名称）
  String? groupNameCn; // 字典名称中文
  String? itemId; // item id
  String? itemName; // item名称（例：商品规格名称；商品属性值）
  String? itemNameCn; // item名称中文
  String? itemAlias; // item别名（例：商品规格别名）
  String? imageUrl; // 图片链接（例：SKU图片）
  String? color; // 颜色值（格式：# + 6位16进制数，例：#CCCCCC）
  bool? isActive; // 是否启动，默认启用（例：商品属性的显示开关）

  GoodsExtendItem({
    this.groupId,
    this.groupName,
    this.groupNameCn,
    this.itemId,
    this.itemName,
    this.itemNameCn,
    this.itemAlias,
    this.imageUrl,
    this.color,
    this.isActive,
  });

  factory GoodsExtendItem.fromJson(dynamic json) {
    return GoodsExtendItem(
      groupId: json?['groupId'] as String?,
      groupName: json?['groupName'] as String?,
      groupNameCn: json?['groupNameCn'] as String?,
      itemId: json?['itemId'] as String?,
      itemName: json?['itemName'] as String?,
      itemNameCn: json?['itemNameCn'] as String?,
      itemAlias: json?['itemAlias'] as String?,
      imageUrl: json?['imageUrl'] as String?,
      color: json?['color'] as String?,
      isActive: json?['isActive'] as bool?,
    );
  }
}

class SkuStepPrice {
  int start;
  int end;
  double? price;
  double? insideOnePrice;

  SkuStepPrice({
    required this.start,
    required this.end,
    this.price,
    this.insideOnePrice,
  });

  factory SkuStepPrice.fromJson(dynamic json) {
    return SkuStepPrice(
      start: json?['start'] as int,
      end: json?['end'] as int,
      price: json?['price'] as double?,
      insideOnePrice: json?['insideOnePrice'] as double?,
    );
  }
}

class MidCartSkuModel {
  String? skuId; // SKU ID（必填）
  String? skuNo; // SKU商品料号（必填）
  List<GoodsExtendItem>? specItemList;
  int? stockQty; // 库存数量
  bool? selected; // 是否已选中
  double? salePrice; // 销售价
  int? buyQty; // 加购数量
  double? subtotalSalePrice; // 销售价小计
  String? skuImage; //商品图片
  String? goodsId; //商品ID
  List<SkuStepPrice>? stepPrices; //阶梯价
  String? spm; //SPM跟踪码
  //最小加购数量（SKU维度一次加购数量，若值为null，则表示取SPU的minIncreaseQuantity）
  int? minIncreaseQuantity;

  MidCartSkuModel({
    this.skuId,
    this.skuNo,
    this.specItemList,
    this.stockQty,
    this.selected,
    this.salePrice,
    this.buyQty,
    this.subtotalSalePrice,
    this.skuImage,
    this.goodsId,
    this.stepPrices,
    this.spm,
    this.minIncreaseQuantity,
  });

  factory MidCartSkuModel.fromJson(dynamic json) {
    List<GoodsExtendItem> specItemList =
        (json?['specItemList'] as List<dynamic>?)
                ?.map((item) => GoodsExtendItem.fromJson(item))
                .toList() ??
            [];

    List<SkuStepPrice> stepPrices = (json?['stepPrices'] as List<dynamic>?)
            ?.map((item) => SkuStepPrice.fromJson(item))
            .toList() ??
        [];

    return MidCartSkuModel(
      skuId: json?['skuId'] as String?,
      skuNo: json?['skuNo'] as String?,
      specItemList: specItemList,
      stockQty: json?['stockQty'] as int?,
      selected: json?['selected'] as bool? ?? false,
      salePrice: json?['salePrice'] as double?,
      buyQty: json?['buyQty'] as int?,
      subtotalSalePrice: json?['subtotalSalePrice'],
      skuImage: json?['skuImage'] as String?,
      goodsId: json?['goodsId'] as String?,
      stepPrices: stepPrices,
      spm: json?['spm'] as String?,
      minIncreaseQuantity: json?['minIncreaseQuantity'] as int?,
    );
  }

  get skuName {
    return specItemList
        ?.map((item) => '${item.groupName}: ${item.itemName}')
        .toList()
        .join('; ');
  }
}

class MidCartGoodsModel {
  String? goodsId; //商品ID
  String? goodsNo; // 商品编号-主数据
  String? goodsName; // 商品名称-主数据
  String? goodsTitle; // 商品标题-主数据
  int? minBuyQuantity; //最小购买数量（起订量）
  String? goodsPriceUnitName; // 商品计价单位名称
  int? minIncreaseQuantity; //最小加购数量
  String? mainImageUrl; //商品主图
  int? selectedGoodsQty; //选中的商品数量
  List<MidCartSkuModel> skuList; // 商品SKU列表
  List<GoodsExtendItem>? specItemList; // 规格参数
  int? skuTotalQuantity; // SKU总数量
  int? skuSelectedQuantity; // SKU选中数量
  bool? skuSelected; // SKU是否已选中
  double? estimateFreight; // 预计运费
  String? routeId; // 线路ID
  String? padc; //促销活动动态代码（与goodsId一起，组成虚拟商品）
  String? paName; //促销活动名称（活动显示名称）
  bool unfold; // 是否展开-辅助字段
  bool? selected; // 是否已选中-辅助字段

  MidCartGoodsModel({
    this.selected = false,
    this.goodsId,
    this.goodsNo,
    this.goodsName,
    this.goodsTitle,
    this.minBuyQuantity,
    this.goodsPriceUnitName,
    this.minIncreaseQuantity,
    this.mainImageUrl,
    required this.skuList,
    this.specItemList,
    this.skuTotalQuantity,
    this.skuSelectedQuantity,
    this.skuSelected,
    this.estimateFreight,
    this.paName,
    this.padc,
    this.unfold = false,
  });

  factory MidCartGoodsModel.fromJson(dynamic json) {
    List<MidCartSkuModel> skuList = (json?['skuList'] as List<dynamic>?)
            ?.map((item) => MidCartSkuModel.fromJson(item))
            .toList() ??
        [];

    List<GoodsExtendItem> specItemList =
        (json?['specItemList'] as List<dynamic>?)
                ?.map((item) => GoodsExtendItem.fromJson(item))
                .toList() ??
            [];

    return MidCartGoodsModel(
      unfold: false,
      selected: false,
      goodsId: json?['goodsId'] as String?,
      goodsNo: json?['goodsNo'] as String?,
      goodsName: json?['goodsName'] as String?,
      goodsTitle: json?['goodsTitle'] as String?,
      minBuyQuantity: json?['minBuyQuantity'] as int?,
      goodsPriceUnitName: json?['goodsPriceUnitName'] as String?,
      minIncreaseQuantity: json?['minIncreaseQuantity'] as int?,
      mainImageUrl: json?['mainImageUrl'] as String?,
      skuList: skuList,
      specItemList: specItemList,
      skuTotalQuantity: json?['skuTotalQuantity'] as int?,
      skuSelectedQuantity: json?['skuSelectedQuantity'] as int?,
      skuSelected: json?['skuSelected'] as bool?,
      estimateFreight: json?['estimateFreight'] as double?,
      padc: json?['padc'] as String?,
      paName: json?['paName'] as String?,
    );
  }
}

class SpecModel {
  List<String>? specName;

  SpecModel({
    this.specName,
  });

  factory SpecModel.fromJson(dynamic json) {
    List<String> values = [];
    if (json != null) {
      json.forEach((v) {
        values.add(v);
      });
    }

    return SpecModel(specName: values);
  }
}

class SkuSpecModel {
  String? specId; //规格ID
  String? specName; //规格名称
  String? itemId; //规格值ID
  String? itemName; //规格值
  String? imageUrl; //图片
  String? color; //颜色

  SkuSpecModel({
    this.specId,
    this.specName,
    this.itemId,
    this.itemName,
    this.imageUrl,
    this.color,
  });

  factory SkuSpecModel.fromJson(dynamic json) {
    return SkuSpecModel(
      specId: json?['specId'] as String?,
      specName: json?['specName'] as String?,
      itemId: json?['itemId'] as String?,
      itemName: json?['itemName'] as String?,
      imageUrl: json?['imageUrl'] as String?,
      color: json?['color'] as String?,
    );
  }
}

class InquirySkuModel {
  String? goodsId; //商品id
  String? goodsName; //商品名称
  String? skuImage; //sku图片
  int? buyQuantity; //购买数量
  String? priceUnitName; //价格单位
  double? salePrice = 6; //销售价
  double? totalSalePrice = 7; //销售总价
  double? supplierPrice = 8; //供应商价格
  String? supplyLink; //货源链接
  int? packageInsideCount = 10; //包装含量
  double? goodsLength = 11; //长
  double? goodsWidth = 12; //宽
  double? goodsHeight = 13; //高
  double? goodsWeight = 14; //重量
  Map<String, SpecModel>? specMap; //specMap
  List<SkuSpecModel>? specs; //规格值

  InquirySkuModel({
    this.goodsId,
    this.goodsName,
    this.skuImage,
    this.buyQuantity,
    this.priceUnitName,
    this.salePrice,
    this.totalSalePrice,
    this.supplierPrice,
    this.supplyLink,
    this.packageInsideCount,
    this.goodsLength,
    this.goodsWidth,
    this.goodsHeight,
    this.goodsWeight,
    this.specMap,
    this.specs,
  });

  factory InquirySkuModel.fromJson(dynamic json) {
    Map<String, SpecModel> specMap = {};
    if (json?['specMap'] != null) {
      json?['specMap'].forEach((key, value) {
        specMap[key] = SpecModel.fromJson(value);
      });
    }

    return InquirySkuModel(
      goodsId: json?['goodsId'] as String?,
      goodsName: json?['goodsName'] as String?,
      skuImage: json?['skuImage'] as String?,
      buyQuantity: json?['buyQuantity'] as int?,
      priceUnitName: json?['priceUnitName'] as String?,
      salePrice: json?['salePrice'] as double?,
      totalSalePrice: json?['totalSalePrice'] as double?,
      supplierPrice: json?['supplierPrice'] as double?,
      supplyLink: json?['supplyLink'] as String?,
      packageInsideCount: json?['packageInsideCount'] as int?,
      goodsLength: json?['goodsLength'] as double?,
      goodsWidth: json?['goodsWidth'] as double?,
      goodsWeight: json?['goodsWeight'] as double?,
    );
  }
}

class InquiryModel {
  String? goodsLookingNo; //询盘单号
  int? totalGoodsCount; // 询盘总数量
  double? subTotal; //询盘总价
  List<InquirySkuModel>? skus; //询盘商品
  String? remark; //客户备注
  String? id; //id
  int? submitTime; //询盘时间
  String? countryName; //国家
  String? whatsapp; //whatsapp
  String? submitName; //姓名
  String? email; //邮箱
  String? postcode; //邮编
  String? address; //地址
  String? countryId; //国家
  String? areaCode; //区号
  bool? isFirstSubmit; //是否首次询盘

  InquiryModel({
    this.goodsLookingNo,
    this.totalGoodsCount,
    this.subTotal,
    this.skus,
    this.remark,
    this.id,
    this.submitTime,
    this.countryName,
    this.whatsapp,
    this.submitName,
    this.email,
    this.postcode,
    this.address,
    this.countryId,
    this.areaCode,
    this.isFirstSubmit,
  });

  factory InquiryModel.fromJson(dynamic json) {
    List<InquirySkuModel> skus = (json?['skus'] as List<dynamic>?)
            ?.map((item) => InquirySkuModel.fromJson(item))
            .toList() ??
        [];

    return InquiryModel(
      goodsLookingNo: json?['goodsLookingNo'] as String?,
      totalGoodsCount: json?['totalGoodsCount'] as int?,
      subTotal: json?['subTotal'] as double?,
      skus: skus,
      remark: json?['remark'] as String?,
      id: json?['id'] as String?,
      submitTime: json?['submitTime'] as int?,
      countryName: json?['countryName'] as String?,
      whatsapp: json?['whatsapp'] as String?,
      submitName: json?['submitName'] as String?,
      email: json?['email'] as String?,
      postcode: json?['postcode'] as String?,
      address: json?['address'] as String?,
      countryId: json?['countryId'] as String?,
      areaCode: json?['areaCode'] as String?,
      isFirstSubmit: json?['isFirstSubmit'] as bool?,
    );
  }
}

class InquiryParam {
  int? siteId;
  String? token; //询盘单号
  List<InquirySkuParam>? params; //询盘商品

  InquiryParam({
    this.siteId,
    this.token,
    this.params,
  });

  Map<String, dynamic> toJson() {
    return {
      'siteId': siteId,
      'token': token,
      'params': params?.map((param) => param.toJson()).toList(),
    };
  }
}

class InquirySkuParam {
  String? skuId; //询盘单号
  int? quantity; //数量
  String? spm; //SPM跟踪码
  String? routeId; //线路ID
  String? padc; //促销活动动态代码

  InquirySkuParam({
    this.skuId,
    this.quantity,
    this.spm,
    this.routeId,
    this.padc,
  });

  // 将 User 对象转换为 Map
  Map<String, dynamic> toJson() {
    return {
      'skuId': skuId,
      'quantity': quantity,
      'spm': spm,
      'padc': padc,
      'routeId': routeId,
    };
  }
}

class CountryModel {
  String? id;
  String? name;
  String? countryName; //国家名称（使用商城前台语言）
  String? countryCnName; //国家中文名
  String? countryEnName; //国家英文名
  String? countryEsName; //国家西文名
  String? countryCodeTwo; //国家二字代码
  String? countryCodeThree; //国家三字代码
  String? areaCode; //区号
  List<String>? phoneRegexes; //电话格式
  String? phoneCount; //电话位数

  CountryModel({
    this.id,
    this.name,
    this.countryName,
    this.countryCnName,
    this.countryEnName,
    this.countryEsName,
    this.countryCodeTwo,
    this.countryCodeThree,
    this.areaCode,
    this.phoneRegexes,
    this.phoneCount,
  });

  factory CountryModel.fromJson(dynamic json) {
    List<String> phoneRegexes = [];
    if (json?['phoneRegexes'] != null) {
      for (var item in json['phoneRegexes']) {
        phoneRegexes.add(item as String);
      }
    }

    return CountryModel(
      id: json['id'],
      name: json['countryEsName'],
      countryName: json['countryName'],
      countryCnName: json['countryCnName'],
      countryEnName: json['countryEnName'],
      countryEsName: json['countryEsName'],
      countryCodeTwo: json['countryCodeTwo'],
      countryCodeThree: json['countryCodeThree'],
      areaCode: json['areaCode'],
      phoneRegexes: phoneRegexes,
      phoneCount: json['phoneCount'],
    );
  }
}

class GetBlogListModel {
  String? id;
  String? articleCode;
  String? title;
  String? logo;
  String? author;
  String? introduce;
  int? cdate;
  int? udate;

  GetBlogListModel({
    this.id,
    this.articleCode,
    this.title,
    this.logo,
    this.author,
    this.introduce,
    this.cdate,
    this.udate,
  });

  factory GetBlogListModel.fromJson(Map<String, dynamic> json) {
    return GetBlogListModel(
      id: json['id'],
      articleCode: json['articleCode'],
      title: json['title'],
      logo: json['logo'],
      author: json['author'],
      introduce: json['introduce'],
      cdate: json['cdate'],
      udate: json['udate'],
    );
  }
}

class GetBlogListResp {
  Result? result;
  PageInfo? page;
  List<GetBlogListModel>? data;

  GetBlogListResp({
    this.result,
    this.page,
    this.data,
  });

  factory GetBlogListResp.fromJson(Map<String, dynamic> json) {
    List<GetBlogListModel> items = (json['data'] as List<dynamic>?)
            ?.map((item) => GetBlogListModel.fromJson(item))
            .toList() ??
        [];

    return GetBlogListResp(
      result: Result.fromJson(json['result']),
      page: PageInfo.fromJson(json['page']),
      data: items,
    );
  }
}

class ArticleCategoryTreeModel {
  String? id;
  String? name;
  String? icon;
  String? parentId;
  int? idx;
  bool? isDefault;
  bool isExpanded;
  List<ArticleCategoryTreeModel>? children;

  ArticleCategoryTreeModel({
    this.id,
    this.name,
    this.icon,
    this.parentId,
    this.idx,
    this.isDefault,
    required this.isExpanded,
    this.children,
  });

  factory ArticleCategoryTreeModel.fromJson(Map<String, dynamic> json) {
    List<ArticleCategoryTreeModel> children = [];

    if (json['children'] != null) {
      json['children'].forEach((v) {
        children.add(ArticleCategoryTreeModel.fromJson(v));
      });
    }

    return ArticleCategoryTreeModel(
      id: json['id'],
      name: json['name'],
      icon: json['icon'],
      parentId: json['parentId'],
      idx: json['idx'],
      isDefault: json['isDefault'],
      isExpanded: false,
      children: children,
    );
  }
}

class MarketingCategoryTreeResp {
  Result? result;
  List<MarketingCategoryModel>? data;

  MarketingCategoryTreeResp({this.result, required this.data});

  factory MarketingCategoryTreeResp.fromJson(Map<String, dynamic> json) {
    List<MarketingCategoryModel> items = (json['data'] as List<dynamic>?)
            ?.map((item) => MarketingCategoryModel.fromJson(item))
            .toList() ??
        [];

    return MarketingCategoryTreeResp(
      result: Result.fromJson(json['result']),
      data: items,
    );
  }
}

class MarketingCategoryModel {
  String? id;
  String? parentId;
  int? cateLevel;
  int? idx;
  String? cateName;
  String? cateAlias;
  String? cateLogo;
  int? goodsCount;
  List<MarketingCategoryModel>? children = [];
  bool? selected;

  MarketingCategoryModel(
      {this.id,
      this.parentId,
      this.cateLevel,
      this.idx,
      this.cateName,
      this.cateAlias,
      this.cateLogo,
      this.goodsCount,
      this.children,
      this.selected});

  factory MarketingCategoryModel.fromJson(Map<String, dynamic> json) {
    List<MarketingCategoryModel> children = [];

    if (json['children'] != null) {
      json['children'].forEach((v) {
        children.add(MarketingCategoryModel.fromJson(v));
      });
    }

    return MarketingCategoryModel(
      id: json['id'],
      parentId: json['parentId'],
      cateLevel: json['cateLevel'],
      idx: json['idx'],
      cateName: json['cateName'],
      cateAlias: json['cateAlias'],
      cateLogo: json['cateLogo'],
      goodsCount: json['goodsCount'],
      children: children,
      selected: false,
    );
  }
}

extension StringExtension on String {
  bool get isValidEmail {
    // 简单的电子邮件验证正则表达式
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(this);
  }
}

class GoodsListDataItemModel {
  String? goodsId;
  String? goodsNo;
  String? goodsName;
  String? goodsTitle;
  String? goodsPriceUnitName;
  CurrencyType? currency;
  double? minPrice;
  double? maxPrice;
  int? minBuyQuantity;
  int? minIncreaseQuantity;
  String? mainImageUrl;
  double? goodsRank;
  double? hitScore;
  String? sourceGoodsId;
  double? boxEstimateFreight;
  int? boxFreightGoodsQty;
  double? pcsEstimateFreight;
  bool selected;

  GoodsListDataItemModel({
    this.goodsId = "",
    this.goodsNo,
    this.goodsName,
    this.goodsTitle,
    this.goodsPriceUnitName,
    this.currency,
    this.minPrice,
    this.maxPrice,
    this.minBuyQuantity,
    this.minIncreaseQuantity,
    this.mainImageUrl,
    this.goodsRank,
    this.hitScore,
    this.sourceGoodsId,
    this.boxEstimateFreight,
    this.boxFreightGoodsQty,
    this.pcsEstimateFreight,
    required this.selected,
  });

  factory GoodsListDataItemModel.fromJson(Map<String, dynamic> json) {
    return GoodsListDataItemModel(
      goodsId: json['goodsId'] as String?,
      goodsNo: json['goodsNo'] as String?,
      goodsName: json['goodsName'] as String?,
      goodsTitle: json['goodsTitle'] as String?,
      goodsPriceUnitName: json['goodsPriceUnitName'] as String?,
      currency:
          strToCurrencyType(json['currency'] as String?), // 调用 fromString 方法
      minPrice: json['minPrice'] as double?,
      maxPrice: json['maxPrice'] as double?,
      minBuyQuantity: json['minBuyQuantity'] as int?,
      minIncreaseQuantity: json['minIncreaseQuantity'] as int?,
      mainImageUrl: json['mainImageUrl'] as String?,
      goodsRank: json['goodsRank'] as double?,
      hitScore: json['hitScore'] as double?,
      sourceGoodsId: json['sourceGoodsId'] as String?,
      boxEstimateFreight: json['boxEstimateFreight'] as double?,
      boxFreightGoodsQty: json['boxFreightGoodsQty'] as int?,
      pcsEstimateFreight: json['pcsEstimateFreight'] as double?,
      selected: false,
    );
  }
}

class GoodsListCategoryFilterModel {
  String? id;
  String? name;
  int? goodsCount;
  bool? selected;
  List<GoodsListCategoryFilterModel>? children;

  GoodsListCategoryFilterModel({
    this.id,
    this.name,
    this.goodsCount,
    this.selected,
    this.children,
  });

  factory GoodsListCategoryFilterModel.fromJson(Map<String, dynamic> json) {
    final children = json['children'] as List<dynamic>?;

    return GoodsListCategoryFilterModel(
        id: json['id'] as String?,
        name: json['name'] as String?,
        goodsCount: json['goodsCount'] as int?,
        selected: json['selected'] as bool?,
        children: children
            ?.map((i) => GoodsListCategoryFilterModel.fromJson(i))
            .toList());
  }
}

// 商品列表已选择的过滤项
class GoodsListSelectedFilterModel {
  String? id;
  String? name;

  GoodsListFilterType? filterType;

  GoodsListSelectedFilterModel({
    this.id,
    this.name,
    this.filterType,
  });

  factory GoodsListSelectedFilterModel.fromJson(Map<String, dynamic> json) {
    return GoodsListSelectedFilterModel(
      id: json['id'] as String?,
      // name: json['name'] as String?,
      // filterType: goodsListFilterTypeFromJSON(json['filterType']),
    );
  }
}

class GoodsListDataModel {
  PageInfo? page;
  String? tokenizeWords;
  List<GoodsListDataItemModel>? goodsList;
  List<GoodsListSelectedFilterModel>? selectedFilters;
  List<GoodsListCategoryFilterModel>? categoryFilters;

  GoodsListDataModel({
    this.page,
    this.goodsList,
    this.tokenizeWords,
    this.selectedFilters,
    this.categoryFilters,
  });

  factory GoodsListDataModel.fromJson(Map<String, dynamic> json) {
    final selectedFilters = json['selectedFilters'] as List<dynamic>?;
    final categoryFilters = json['categoryFilters'] as List<dynamic>?;

    return GoodsListDataModel(
      tokenizeWords: json['tokenizeWords'],
      page: PageInfo.fromJson(json['page']),
      goodsList: (json['goodsList'] as List)
          .map((i) => GoodsListDataItemModel.fromJson(i))
          .toList(),
      selectedFilters: selectedFilters
          ?.map((i) => GoodsListSelectedFilterModel.fromJson(i))
          .toList(),
      categoryFilters: categoryFilters
          ?.map((i) => GoodsListCategoryFilterModel.fromJson(i))
          .toList(),
    );
  }
}

class GoodsListDataResp {
  Result? result;
  GoodsListDataModel? data;

  GoodsListDataResp({
    this.result,
    this.data,
  });

  factory GoodsListDataResp.fromJson(Map<String, dynamic> json) {
    return GoodsListDataResp(
      result: Result.fromJson(json['result']),
      data: GoodsListDataModel.fromJson(json['data']),
    );
  }
}

class GoodsListPageResp {
  Result? result;
  GoodsListPageModel? data;

  GoodsListPageResp({
    this.result,
    this.data,
  });

  factory GoodsListPageResp.fromJson(Map<String, dynamic> json) {
    return GoodsListPageResp(
      result: Result.fromJson(json['result']),
      data: GoodsListPageModel.fromJson(json['data']),
    );
  }
}

class GoodsListPageModel {
  MallCategoryTreeModel? categoryTree;
  List<MallCategoryPathItemModel>? categoryPath;
  MidCartModel? cartInfo;
  GoodsListDataModel? pageData;

  GoodsListPageModel({
    this.categoryTree,
    this.categoryPath,
    this.cartInfo,
    this.pageData,
  });

  factory GoodsListPageModel.fromJson(Map<String, dynamic> json) {
    return GoodsListPageModel(
      categoryTree: MallCategoryTreeModel.fromJson(json['categoryTree']),
      categoryPath: (json['categoryPath'] as List)
          .map((item) => MallCategoryPathItemModel.fromJson(item))
          .toList(),
      cartInfo: MidCartModel.fromJson(json['cartInfo']),
      pageData: GoodsListDataModel.fromJson(json['pageData']),
    );
  }
}

// 商品SKU规格
class GoodsSkuSpecModel {
  String id; //规格ID（必填）
  String? name; //规格名称（必填）
  List<GoodsSkuSpecItemModel>? items; //规格明细（必填）

  GoodsSkuSpecModel({
    required this.id,
    this.name,
    this.items,
  });

  factory GoodsSkuSpecModel.fromJson(Map<String, dynamic> json) {
    return GoodsSkuSpecModel(
      id: json['id'],
      name: json['name'],
      items: json['items'] != null
          ? (json['items'] as List)
              .map((i) => GoodsSkuSpecItemModel.fromJson(i))
              .toList()
          : null,
    );
  }
}

// 商品SKU中的规格ID与规格明细ID组合（通过检索SKU与反查有SKU规格）
class GoodsSkuSpecIdPairModel {
  String specId; // 规格ID
  String itemId; // 规格明细ID（当前商品内唯一）
  String? itemName; // 规格项名称

  GoodsSkuSpecIdPairModel({
    required this.itemId,
    this.itemName,
    required this.specId,
  });

  factory GoodsSkuSpecIdPairModel.fromJson(Map<String, dynamic> json) {
    return GoodsSkuSpecIdPairModel(
      itemId: json['itemId'],
      itemName: json['itemName'],
      specId: json['specId'],
    );
  }
}

// 商品SKU信息
class GoodsSkuInfoModel {
  String? id; // SKU ID（必填）
  String? skuNo; // SKU商品料号（必填）
  String? skuName; // SKU商品料号（必填）
  int? stockQty; //库存数量（必填）
  double? price;
  List<GoodsSkuSpecIdPairModel>? specItems; //SKU规格ID组：规格ID，规格明细ID
  List<SkuStepPrice>? stepPrices; // 阶梯价（价格单位在商品级别描述；“end=-1”表示不限）
  int cartQty; //购物车中的数量（必填，默认0）
  bool selected;
  int? minIncreaseQuantity;

  GoodsSkuInfoModel(
      {this.id,
      this.skuNo,
      this.skuName,
      this.stockQty,
      this.specItems,
      this.stepPrices,
      required this.cartQty,
      this.price,
      required this.selected,
      this.minIncreaseQuantity});

  factory GoodsSkuInfoModel.fromJson(Map<String, dynamic> json) {
    return GoodsSkuInfoModel(
      id: json['id'],
      skuNo: json['skuNo'],
      skuName: '',
      price: 0.0,
      stockQty: json['stockQty'],
      cartQty: json['cartQty'] ?? 0,
      specItems: json['specItems'] != null
          ? (json['specItems'] as List)
              .map((i) => GoodsSkuSpecIdPairModel.fromJson(i))
              .toList()
          : null,
      stepPrices: json['stepPrices'] != null
          ? (json['stepPrices'] as List)
              .map((i) => SkuStepPrice.fromJson(i))
              .toList()
          : null,
      selected: false,
      minIncreaseQuantity: json['minIncreaseQuantity'],
    );
  }

  // 深拷贝的copyWith方法
  GoodsSkuInfoModel copyWith({
    String? id,
    String? skuNo,
    String? skuName,
    int? stockQty,
    double? price,
    List<GoodsSkuSpecIdPairModel>? specItems,
    List<SkuStepPrice>? stepPrices,
    required int cartQty,
    required bool selected,
  }) {
    return GoodsSkuInfoModel(
      id: id ?? this.id,
      skuNo: skuNo ?? this.skuNo,
      skuName: skuName ?? this.skuName,
      stockQty: stockQty ?? this.stockQty,
      price: price ?? this.price,
      cartQty: cartQty,
      selected: selected,
      specItems: specItems ?? this.specItems,
      stepPrices: stepPrices ?? this.stepPrices,
    );
  }
}

// 商品SKU规格明细（含库存数量）
class GoodsSkuSpecItemModel {
  String itemId; // 规格明细ID（必填）
  String? itemName; // 规格明细名称（如颜色规格中的“红色”，必填）
  int? stockQty; // 相关SKU总库存数量（必填）
  String? imageUrl; // 图片链接（例：SKU图片）
  String? color; // 颜色值（格式：# + 6位16进制数，例：#CCCCCC）
  int? cartQty; //相关SKU在购物车中的总数量（大于0传值，仅在第1个规格项传值）
  bool disabled;
  bool selected;

  GoodsSkuSpecItemModel({
    required this.itemId,
    this.itemName,
    this.stockQty,
    this.imageUrl,
    this.color,
    this.cartQty,
    required this.disabled,
    required this.selected,
  });

  factory GoodsSkuSpecItemModel.fromJson(Map<String, dynamic> json) {
    return GoodsSkuSpecItemModel(
      itemId: json['itemId'],
      itemName: json['itemName'],
      stockQty: json['stockQty'],
      imageUrl: json['imageUrl'] ?? '',
      color: json['color'],
      cartQty: json['cartQty'],
      disabled: false,
      selected: false,
    );
  }
}

class GoodsPriceRange {
  int? start = 10; // 阶梯价起始值（从 minBuyQuantity 开始）
  int? end = 20; // 阶梯价结束值（-1表示不限）
  double? minPrice = 30; // 最低价
  double? maxPrice = 40; // 最高价（最低价与最高价相同时，前段仅显示一个价格）

  GoodsPriceRange({
    this.start,
    this.end,
    this.minPrice,
    this.maxPrice,
  });

  factory GoodsPriceRange.fromJson(Map<String, dynamic> json) {
    return GoodsPriceRange(
      start: json['start'],
      end: json['end'],
      minPrice: json['minPrice'],
      maxPrice: json['maxPrice'],
    );
  }
}

// 商品属性
class GoodsAttrModel {
  String? attrId; //属性ID
  String? attrName; //属性名称
  String? attrValue; //属性值（多个属性值用空格分隔）
  GoodsAttributeItemType? attributeItemType; //属性类型
  List<String>? values; //属性值（数组类型，支持多个）

  GoodsAttrModel({
    this.attrId,
    this.attrName,
    this.attrValue,
    this.attributeItemType,
    this.values,
  });

  factory GoodsAttrModel.fromJson(Map<String, dynamic> json) {
    return GoodsAttrModel(
      attrId: json['attrId'],
      attrName: json['attrName'],
      attrValue: json['attrValue'],
      attributeItemType:
          stringToGoodsAttributeItemType(json['attributeItemType']),
      values: json['values'] != null
          ? (json['values'] as List).map((i) => i as String).toList()
          : null,
    );
  }
}

class GoodsInfoModel {
  String? id; //商品ID
  String? goodsNo; // 商品编号
  String? goodsName; // 商品名称
  String? goodsTitle; // 商品标题
  bool? isOnline; //是否已上架（商品预览功能可包含未上架商品）
  String? goodsPriceUnitName; // 价格单位
  CurrencyType? currency; // 价格对应的货币
  double? minPrice; // 最低价
  double? maxPrice; // 最高价（若最低价与最高价相同，则在列表显示一个价格）
  int? minBuyQuantity; //最小购买数量（起订量）
  int minIncreaseQuantity; //最小加购数量（一次加购数量）
  String? coverImage; // 视频封面图片（若非空，则在商详页优先显示视频）
  String? videoUrl; // 视频
  List<String>? goodsImageList; // 商品图片组-附属属性
  double? goodsLength; // 商品长（单位：cm）
  double? goodsWidth; // 商品宽（单位：cm）
  double? goodsHeight; // 商品高（单位：cm）
  double? goodsWeight; // 商品重量-附属属性
  String? goodsDesc; // 商品描述（根据访问设备参数，自动取值PC或H5版的描述）
  List<GoodsPriceRange>? goodsPriceRanges; // 商品阶梯价范围（上架商品至少存在一个阶梯价范围）
  List<GoodsAttrModel>? attrList; // 属性参数-附属属性
  List<GoodsSkuSpecModel> specList; // 商品规格列表（必填）
  List<GoodsSkuInfoModel>? skuList; // SKU列表（必填）
  double? boxEstimateFreight; // 单箱预估运费（按商品维度最小装箱数计算）
  int? boxFreightGoodsQty; // 参与单箱预估运费的商品数量
  double? pcsEstimateFreight; // 单件预估运费（1件的预估运费）
  String? routeId; //购物车中选中的线路ID

  GoodsInfoModel({
    this.id,
    this.goodsNo,
    this.goodsName,
    this.goodsTitle,
    this.isOnline,
    this.goodsPriceUnitName,
    this.currency,
    this.minPrice,
    this.maxPrice,
    this.minBuyQuantity,
    required this.minIncreaseQuantity,
    this.coverImage,
    this.videoUrl,
    this.goodsImageList,
    this.goodsLength,
    this.goodsWidth,
    this.goodsHeight,
    this.goodsWeight,
    this.goodsDesc,
    this.goodsPriceRanges,
    this.attrList,
    required this.specList,
    this.skuList,
    this.boxEstimateFreight,
    this.boxFreightGoodsQty,
    this.pcsEstimateFreight,
    this.routeId,
  });

  factory GoodsInfoModel.fromJson(Map<String, dynamic> json) {
    return GoodsInfoModel(
      id: json['id'] ?? '',
      goodsNo: json['goodsNo'] ?? '',
      goodsName: json['goodsName'] ?? '',
      goodsTitle: json['goodsTitle'] ?? '',
      isOnline: json['isOnline'] ?? false,
      goodsPriceUnitName: json['goodsPriceUnitName'] ?? '',
      currency:
          strToCurrencyType(json['currency'] as String?), // 调用 fromString 方法
      minPrice: json['minPrice'] ?? 0.0,
      maxPrice: json['maxPrice'] ?? 0.0,
      minBuyQuantity: json['minBuyQuantity'] ?? 0,
      minIncreaseQuantity: json['minIncreaseQuantity'] ?? 1,
      coverImage: json['coverImage'] ?? '',
      videoUrl: json['videoUrl'] ?? '',
      goodsImageList: json['goodsImageList'] != null
          ? (json['goodsImageList'] as List).map((i) => i as String).toList()
          : null,
      goodsLength: json['goodsLength'] ?? 0.0,
      goodsWidth: json['goodsWidth'] ?? 0.0,
      goodsHeight: json['goodsHeight'] ?? 0.0,
      goodsWeight: json['goodsWeight'] ?? 0.0,
      goodsDesc: json['goodsDesc'] ?? '',
      goodsPriceRanges: json['goodsPriceRanges'] != null
          ? (json['goodsPriceRanges'] as List)
              .map((i) => GoodsPriceRange.fromJson(i))
              .toList()
          : null,
      attrList: json['attrList'] != null
          ? (json['attrList'] as List)
              .map((i) => GoodsAttrModel.fromJson(i))
              .toList()
          : null,
      specList: json['specList'] != null
          ? (json['specList'] as List)
              .map((i) => GoodsSkuSpecModel.fromJson(i))
              .toList()
          : [],
      skuList: json['skuList'] != null
          ? (json['skuList'] as List)
              .map((i) => GoodsSkuInfoModel.fromJson(i))
              .toList()
          : null,
      boxEstimateFreight: json['boxEstimateFreight'] ?? 0,
      boxFreightGoodsQty: json['boxFreightGoodsQty'] ?? 0,
      pcsEstimateFreight: json['pcsEstimateFreight'] ?? 0,
      routeId: json['routeId'] ?? '',
    );
  }
}

class MidCartSkuAddParam {
  String? skuId; // 商品SKU ID
  int? quantity; // 数量
  String? spm; //SPM跟踪码

  MidCartSkuAddParam({
    this.skuId,
    this.quantity,
    this.spm,
  });

  factory MidCartSkuAddParam.fromJson(Map<String, dynamic> json) {
    return MidCartSkuAddParam(
        skuId: json['skuId'], quantity: json['quantity'], spm: json['spm']);
  }

  Map<String, dynamic> toJson() {
    return {
      'skuId': skuId,
      'quantity': quantity,
      'spm': spm,
    };
  }
}

class CalculateEstimateFreightParam {
  int? siteId; // 当前站点ID（即页面选中的配送国家ID；必填）
  String? routeId; //当前页面选中的线路（不传，则取默认值）
  bool? ignoreRouteIdError; //忽略线路ID错误（从购物车中取到的线路，作为默认传参时，将此参数置为true）
  List<MidCartSkuAddParam>? skuList;

  CalculateEstimateFreightParam({
    this.siteId,
    this.routeId,
    this.ignoreRouteIdError,
    this.skuList,
  });

  Map<String, dynamic> toJson() {
    return {
      'siteId': siteId,
      'routeId': routeId,
      'ignoreRouteIdError': ignoreRouteIdError,
      'skuList': skuList?.map((i) => i.toJson()).toList(),
    };
  }
}

//线路运费对象
class CalculateRouteFreightModel {
  String? routeId; //属性ID
  String? routeName; //属性名称
  String? deliverTimeName; //属性值（多个属性值用空格分隔）
  double? totalEstimateFreight; //预估运费总计（全部SKU可预估运费时，才返回）
  double? partEstimateFreight; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int? freightGoodsQty; //参与预估运费的商品数量
  bool selected; //是否选中

  CalculateRouteFreightModel({
    this.routeId,
    this.routeName,
    this.deliverTimeName,
    this.totalEstimateFreight,
    this.partEstimateFreight,
    this.freightGoodsQty,
    required this.selected,
  });

  factory CalculateRouteFreightModel.fromJson(Map<String, dynamic> json) {
    return CalculateRouteFreightModel(
        routeId: json['routeId'],
        routeName: json['routeName'],
        deliverTimeName: json['deliverTimeName'],
        totalEstimateFreight: json['totalEstimateFreight'],
        partEstimateFreight: json['partEstimateFreight'],
        freightGoodsQty: json['freightGoodsQty'],
        selected: json['selected'] ?? false);
  }
}

// 预估运费接口返回对象
class CalculateEstimateFreightModel {
  double? totalEstimateFreight;
  double? partEstimateFreight;
  int? freightGoodsQty;
  String? selectedRouteId;
  List<CalculateRouteFreightModel>? routeList;

  CalculateEstimateFreightModel({
    this.totalEstimateFreight,
    this.partEstimateFreight,
    this.freightGoodsQty,
    this.selectedRouteId,
    this.routeList,
  });

  factory CalculateEstimateFreightModel.fromJson(Map<String, dynamic> json) {
    List<CalculateRouteFreightModel> routeList =
        (json['routeList'] as List<dynamic>?)
                ?.map((item) => CalculateRouteFreightModel.fromJson(item))
                .toList() ??
            [];

    return CalculateEstimateFreightModel(
        totalEstimateFreight: json['totalEstimateFreight'],
        partEstimateFreight: json['partEstimateFreight'],
        freightGoodsQty: json['freightGoodsQty'],
        selectedRouteId: json['selectedRouteId'],
        routeList: routeList);
  }
}

//预估运费接口返回
class CalculateEstimateFreightResp {
  Result? result;
  CalculateEstimateFreightModel data;

  CalculateEstimateFreightResp({
    this.result,
    required this.data,
  });

  factory CalculateEstimateFreightResp.fromJson(Map<String, dynamic> json) {
    return CalculateEstimateFreightResp(
        result: Result.fromJson(json['result']),
        data: CalculateEstimateFreightModel.fromJson(json['data']));
  }
}

class NameValueModel {
  String? name;
  String? value;

  NameValueModel({this.name, this.value});

  factory NameValueModel.fromJson(Map<String, dynamic> json) {
    return NameValueModel(name: json['name'], value: json['value']);
  }
}

class LoginUserModel {
  String? userId;
  String? username;

  LoginUserModel({this.userId, this.username});

  factory LoginUserModel.fromJson(Map<String, dynamic> json) {
    return LoginUserModel(userId: json['userId'], username: json['username']);
  }
}

class SiteListModel {
  int id;
  String? code;
  String? name;
  String? logo;
  bool selected;

  SiteListModel(
      {required this.id,
      this.code,
      this.name,
      this.logo,
      required this.selected});

  factory SiteListModel.fromJson(Map<String, dynamic> json) {
    return SiteListModel(
      id: json['id'] ?? 0,
      code: json['code'] ?? "",
      name: json['name'] ?? "",
      logo: json['logo'] ?? "",
      selected: false,
    );
  }

  SiteListModel copyWith({
    int? id,
    String? code,
    String? name,
    String? logo,
    bool? selected,
  }) {
    return SiteListModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      logo: logo ?? this.logo,
      selected: selected ?? this.selected,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'logo': logo,
    };
  }
}

class PageClickIncomingModel {
  String? visitCode;
  String? headFirstScript;
  String? headLastScript;
  String? abtestMode;
  String? defaultCountryCode;
  int? siteId;
  List<NameValueModel>? responseHeaders;
  LoginUserModel? loginUser;
  List<SiteListModel>? siteList;

  PageClickIncomingModel({
    this.visitCode,
    this.headFirstScript,
    this.headLastScript,
    this.abtestMode,
    this.defaultCountryCode,
    this.siteId,
    this.responseHeaders,
    this.loginUser,
    this.siteList,
  });

  factory PageClickIncomingModel.fromJson(Map<String, dynamic> json) {
    List<NameValueModel> responseHeaders =
        (json['responseHeaders'] as List<dynamic>?)
                ?.map((item) => NameValueModel.fromJson(item))
                .toList() ??
            [];
    List<SiteListModel> siteList = (json['siteList'] as List<dynamic>?)
            ?.map((item) => SiteListModel.fromJson(item))
            .toList() ??
        [];

    return PageClickIncomingModel(
        visitCode: json['visitCode'],
        headFirstScript: json['headFirstScript'],
        headLastScript: json['headLastScript'],
        abtestMode: json['abtestMode'],
        defaultCountryCode: json['defaultCountryCode'],
        siteId: json['siteId'],
        responseHeaders: responseHeaders,
        siteList: siteList,
        loginUser: LoginUserModel.fromJson(json['loginUser'] ?? {}));
  }
}

// 加购参数
class AddCartParam {
  String? goodsId; // 商品ID
  List<AddCartSkuParam>? skus; // 商品属性

  AddCartParam({this.goodsId, this.skus});

  // 将 User 对象转换为 Map
  Map<String, dynamic> toJson() {
    return {
      'goodsId': goodsId,
      'skus': skus?.map((sku) => sku.toJson()).toList(),
    };
  }
}

// 加购SKU参数
class AddCartSkuParam {
  String? skuId; // 商品SKU
  int? quantity; // 数量
  String? spm; // SPM跟踪码

  AddCartSkuParam({this.skuId, this.quantity, this.spm});

  Map<String, dynamic> toJson() {
    return {
      'skuId': skuId,
      'quantity': quantity,
      'spm': spm,
    };
  }
}

class MidCartAddParam {
  String? goodsId; // 商品ID
  int? siteId; // 当前站点ID（即页面选中的配送国家ID）
  String? routeId; // 线路ID
  List<MidCartSkuAddParam>? skus; //sku列表

  MidCartAddParam({this.goodsId, this.siteId, this.routeId, this.skus});

  factory MidCartAddParam.fromJson(Map<String, dynamic> json) {
    List<MidCartSkuAddParam> skus = (json['skus'] as List<dynamic>?)
            ?.map((item) => MidCartSkuAddParam.fromJson(item))
            .toList() ??
        [];

    return MidCartAddParam(
      goodsId: json['goodsId'],
      siteId: json['siteId'],
      routeId: json['routeId'],
      skus: skus,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'goodsId': goodsId ?? "",
      'siteId': siteId ?? "",
      'routeId': routeId ?? "",
      'skus': skus?.map((i) => i.toJson()).toList(),
    };
  }
}

class UserModel {
  String? id;
  String? realName; //姓名
  String? email; //邮箱
  String? countryId;
  String? countryName; //国家
  String? whatsapp;
  String? inviteCode; //邀请码
  int? inquiryCount; //询盘总数量

  UserModel(
      {this.id,
      this.realName,
      this.email,
      this.countryId,
      this.countryName,
      this.whatsapp,
      this.inviteCode,
      this.inquiryCount});

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['userId'] as String?,
      realName: json['realName'] as String?,
      email: json['email'] as String?,
      countryId: json['countryId'] as String?,
      countryName: json['countryName'] as String?,
      whatsapp: json['whatsapp'] as String?,
      inviteCode: json['inviteCode'] as String?,
      inquiryCount: json['inquiryCount'] as int? ?? 0,
    );
  }
}

class AuthLoginModel {
  String? userId; // 用户ID
  String? username; // 用户名
  String? token; // 登录令牌

  AuthLoginModel({this.userId, this.username, this.token});

  factory AuthLoginModel.fromJson(Map<String, dynamic> json) {
    return AuthLoginModel(
      userId: json['userId'] as String?,
      username: json['username'] as String?,
      token: json['token'] as String?,
    );
  }
}
