import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/pages/product/product_page.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';

class ProductCard extends StatelessWidget {
  final String? imageUrl;
  final String title;
  final String? price;
  final double? minPrice;
  final double? maxPrice;
  final String productId;
  final double imageHeight;
  final double? titleFontSize;
  final double? priceFontSize;

  const ProductCard({
    super.key,
    this.imageUrl,
    required this.title,
    this.price,
    this.minPrice,
    this.maxPrice,
    required this.productId,
    this.titleFontSize = 8.0,
    this.priceFontSize = 8.0,
    required this.imageHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      child: InkWell(
        onTap: () {
          Get.toNamed(AppRoutes.ProductPage,
              arguments: {'productId': productId});
        }, // 绑定点击事件
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(10)),
              child: CachedNetworkImage(
                imageUrl: imageUrl ?? "", // 图片地址
                height: imageHeight,
                width: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.error),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 2.0, top: 4.0),
              child: Text(title,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey.shade600,
                    backgroundColor: Colors.white,
                  )),
            ),
            Padding(
              padding: EdgeInsets.only(left: 2.0, top: 2.0),
              child: Text(
                  minPrice != maxPrice
                      ? '${setUnit(minPrice ?? 0)}-$maxPrice'
                      : setUnit(maxPrice ?? 0),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: priceFontSize,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                    backgroundColor: Colors.white,
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
