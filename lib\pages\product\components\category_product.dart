import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/goods_price.dart';
import 'package:chilat2_mall_app/pages/product/product_page.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CategoryProduct extends StatefulWidget {
  final dynamic data;
  final Color color;
  const CategoryProduct({super.key, required this.data, required this.color});

  @override
  State<CategoryProduct> createState() => _CategoryProductState();
}

class _CategoryProductState extends State<CategoryProduct> {
  final double _widgetHeight = 230.0;
  final double _goodsWidth = 150.0;
  final double _imageHeight = 128.0;
  final double _bannerHeight = 90.0;
  final double _horizonHeight = 180.0;
  final double _priceFontSize = 10.0;
  final double _titleFontSize = 10.0;
  final double _borderRadius = 4.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: _widgetHeight, // 设置容器高度
      margin: const EdgeInsets.symmetric(vertical: 0),
      child: Stack(
        children: <Widget>[
          // 标题和按钮
          Positioned(
            top: 5,
            left: 0,
            child: Container(
              height: _bannerHeight,
              width: MediaQuery.of(context).size.width,
              color: widget.color,
              padding: EdgeInsets.all(10),
              child: Text(
                widget.data?['tagName'] ?? '',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.0,
                    fontWeight: FontWeight.w500),
              ),
            ),
          ),
          Positioned(
            top: 5,
            right: 5,
            child: IconButton(
              color: Colors.white,
              icon: Icon(Icons.arrow_forward),
              onPressed: () {
                // 在这里添加点击按钮后的操作逻辑
                print('IconButton clicked');
              },
            ),
          ),
          // 商品列表
          Positioned(
            top: 50.0, // 设置商品列表的顶部位置
            left: 0,
            right: 0,
            child: SizedBox(
              height: _horizonHeight, // 设置水平滚动区域的高度
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.data?['goodsList']?.length,
                itemBuilder: (BuildContext context, int index) {
                  dynamic product = widget.data?['goodsList'][index] ?? {};
                  return buildProductCard(product);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildProductCard(dynamic product) {
    return Container(
      width: _goodsWidth,
      padding: const EdgeInsets.only(bottom: 4),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_borderRadius),
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: GestureDetector(
          onTap: () {
            Get.toNamed(AppRoutes.ProductPage,
                arguments: {'productId': product['goodsId'] ?? ""});
          },
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            // 商品图片
            Expanded(
              child: ClipRRect(
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(_borderRadius)),
                child: CachedNetworkImage(
                  imageUrl: product?['mainImageUrl'] ?? '',
                  fit: BoxFit.fill,
                  width: MediaQuery.sizeOf(context).width,
                  height: _imageHeight,
                ),
              ),
            ),
            // 商品名称
            Padding(
              padding: const EdgeInsets.only(left: 4.0, top: 2.0),
              child: Text(
                product?['goodsName'] ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: _titleFontSize,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            // 商品价格
            Padding(
              padding: EdgeInsets.only(left: 4.0, top: 2.0, bottom: 2),
              child: GoodsPriceUnit(
                  minPrice: product?['minPrice'],
                  maxPrice: product?['maxPrice'],
                  textStyle: TextStyle(
                    fontSize: _priceFontSize,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  )),
            ),
          ])),
    );
  }
}
