import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:get/get.dart';

class MainController extends GetxController {
  // 1. 定义可观察状态
  final timeoutMap = TimeoutMap<String, dynamic>();

  // 2. 生命周期方法（可选）
  @override
  void onInit() {
    super.onInit();
    // 初始化逻辑（仅调用一次）
    onCategoryTree();
  }

  @override
  void onReady() {
    super.onReady();
    // 视图渲染完成后调用
    print('Controller ready');
  }

  Future<MarketingCategoryTreeResp?> onCategoryTree(
      {int timeoutSeconds = 600}) async {
    try {
      dynamic res = await timeoutMap.get(
          'useCategoryTree', ProductAPI.useCategoryTree({}),
          timeoutSeconds: timeoutSeconds);
      if (res?['result']?['code'] == 200) {
        return MarketingCategoryTreeResp.fromJson(res);
      }
      return null;
    } catch (e) {
      showErrorMessage(e.toString());
    }
    return null;
  }

  // 首页数据查询
  Future<dynamic> useHomePageData({int timeoutSeconds = 600}) async {
    try {
      dynamic res = await timeoutMap.get(
          'useHomePageData', HomeAPI.useHomePageData({}),
          timeoutSeconds: timeoutSeconds);
      if (res?['result']?['code'] == 200) {
        return res?['data'];
      }
      return null;
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 推荐商品
  Future<dynamic> useRecommendGoodsV2(dynamic data,
      {int timeoutSeconds = 600}) async {
    try {
      dynamic res = await timeoutMap.get(
          genSecureHash(['useRecommendGoodsV2', data]),
          HomeAPI.useRecommendGoodsV2(data),
          timeoutSeconds: timeoutSeconds);
      if (res?['result']?['code'] == 200) {
        return res?['data'];
      }
      return null;
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 首页数据查询
  Future<dynamic> useGoodsPageListData(dynamic data,
      {int timeoutSeconds = 600}) async {
    try {
      dynamic res = await timeoutMap.get(
          genSecureHash(['useHomePageData', data]),
          HomeAPI.useGoodsPageListData(data),
          timeoutSeconds: timeoutSeconds);
      if (res?['result']?['code'] == 200) {
        return res?['data'];
      }
      return null;
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }
}
