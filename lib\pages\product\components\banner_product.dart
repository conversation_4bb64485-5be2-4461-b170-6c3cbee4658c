import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BannerProduct extends StatefulWidget {
  final String? banner;
  final dynamic data;
  const BannerProduct({super.key, this.data, this.banner});

  @override
  State<BannerProduct> createState() => _BannerProductState();
}

class _BannerProductState extends State<BannerProduct> {
  final double _goodsWidth = 150.0.sp;
  final double _imageHeight = 128.0.sp;
  final double _bannerHeight = 90.0.sp;
  final double _horizonHeight = 180.0.sp;
  final double _priceFontSize = 12.0.sp;
  final double _titleFontSize = 12.0.sp;
  final double _borderRadius = 4.0.sp;

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.only(top: 0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
            padding: const EdgeInsets.only(top: 6, bottom: 2),
            child: GestureDetector(
              onTap: () {
                NavigatorUtil.pushNamed(context, AppRoutes.ProductList);
              },
              child: CachedNetworkImage(
                imageUrl: widget.banner ?? '',
                fit: BoxFit.fill,
                width: MediaQuery.sizeOf(context).width,
                height: _bannerHeight,
              ),
            ),
          ),
          SizedBox(
            height: _horizonHeight,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.data?['hotSaleGoods'].length,
              itemBuilder: (context, index) {
                dynamic product = widget.data?['hotSaleGoods']?[index];
                return buildProductCard(product);
              },
            ),
          ),
        ]));
  }

  Widget buildProductCard(dynamic product) {
    // 添加null检查
    if (product == null) {
      return SizedBox(width: _goodsWidth);
    }

    dynamic price = product['minPrice'] != product['maxPrice']
        ? '${setUnit(product['minPrice'] ?? 0)}-${setUnit(product['maxPrice'] ?? 0)}'
        : setUnit(product['maxPrice'] ?? 0);

    return Container(
        width: _goodsWidth,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_borderRadius),
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: GestureDetector(
          onTap: () {
            // TODO 待完善
            Get.toNamed(AppRoutes.ProductPage,
                arguments: {'productId': product['goodsId']});
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 商品图片
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.vertical(
                      top: Radius.circular(_borderRadius)),
                  child: CachedNetworkImage(
                    imageUrl: product['mainImageUrl']?.toString() ?? '',
                    fit: BoxFit.fill,
                    width: MediaQuery.of(context).size.width,
                    height: _imageHeight,
                  ),
                ),
              ),
              // 商品名称
              Padding(
                padding: const EdgeInsets.only(left: 4.0, top: 2.0),
                child: Text(
                  product['goodsName']?.toString() ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: _titleFontSize,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
              // 商品价格
              Padding(
                padding: EdgeInsets.only(left: 4.0, top: 2.0, bottom: 2),
                child: Text(
                  price.toString(),
                  style: TextStyle(
                    fontSize: _priceFontSize,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
