import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/home/<USER>/choose_us.dart';
import 'package:chilat2_mall_app/pages/home/<USER>/head_swiper.dart';
import 'package:chilat2_mall_app/pages/home/<USER>/news.dart';
import 'package:chilat2_mall_app/pages/home/<USER>/notice.dart';
import 'package:chilat2_mall_app/pages/home/<USER>/process.dart';
import 'package:chilat2_mall_app/pages/main/main_controller.dart';
import 'package:chilat2_mall_app/pages/product/components/banner_product.dart';
import 'package:chilat2_mall_app/pages/product/components/category_product.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _isLoading = true;
  late Future<void> _initFuture;
  dynamic pageData = {"data": {}};
  final MainController mainController = Get.put(MainController());

  String mercadoHotBanner =
      'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/71916396-43b2-4c93-918d-2033b54b7716.jpg';
  String yiwuHotBanner =
      'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/82457c83-feaa-46f1-ba2c-935a49d2c034.jpg';
  String loginBg =
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/27/afb3f499-42c5-4ca4-8f0d-760e36a4b591.png";
  String noticeLogo =
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/268c3fd6-85ef-4db5-8640-549d908d570b.png";

  // 选择我们
  final List<String> carouselChooseData = [
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
  ];
  // 人们对我们的看法
  final List<YouTubeVideoItem> userVideoData = [
    YouTubeVideoItem(
      id: "TROzVaB3Lr0",
      videoBg:
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
    ),
    YouTubeVideoItem(
      id: "Tj0nrnhxgXw",
      videoBg:
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
    ),
    YouTubeVideoItem(
      id: "_omi5a-pHkA",
      videoBg:
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
    ),
    YouTubeVideoItem(
      id: "4FVIz0PvEcE",
      videoBg:
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
    ),
  ];
  // final MainController mainController = Get.put(MainController());

  @override
  void initState() {
    super.initState();
    _initFuture = onPageData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> onPageData() async {
    try {
      await Global.setCartList();
      final List<dynamic> responses = await Future.wait([
        // HomeAPI.useHomePageData({}),
        // HomeAPI.useRecommendGoodsV2({
        //   "goodsCount": 24,
        //   "deviceType": 2,
        // }),
        // HomeAPI.useGoodsPageListData({
        //   "tagIds": ["20000100"]
        // }),
        // HomeAPI.useGoodsPageListData({
        //   "tagIds": ["20001100"]
        // }),
        mainController.useHomePageData(timeoutSeconds: 3600),
        mainController.useRecommendGoodsV2({
          "goodsCount": 24,
          "deviceType": 2,
        }, timeoutSeconds: 3600),
        mainController.useGoodsPageListData({
          "tagIds": ["20000100"]
        }, timeoutSeconds: 3600),
        mainController.useGoodsPageListData({
          "tagIds": ["20001100"]
        }, timeoutSeconds: 3600),
      ]);
      print("==>>TODO 1251: ${responses[0]}");
      print("==>>TODO 1252: ${responses[1]}");
      print("==>>TODO 1253: ${responses[2]}");
      print("==>>TODO 1254: ${responses[3]}");
      setState(() {
        pageData['data'] = responses[0];
        pageData['recommend'] = responses[1];
        pageData['cameraInfo'] = {
          "tagId": "20000100",
          "tagName": I18n.of(context)?.translate('cm_guestHome.addGoods'),
          "goodsList": responses[2]['goodsList'].sublist(
              0,
              responses[2]['goodsList'].length < 10
                  ? responses[2]['goodsList'].length
                  : 10),
        };
        pageData['humidifierInfo'] = {
          "tagId": "20000100",
          "tagName": I18n.of(context)?.translate('cm_guestHome.addGoods'),
          "goodsList": responses[3]['goodsList'].sublist(
              0,
              responses[3]['goodsList'].length < 10
                  ? responses[3]['goodsList'].length
                  : 10),
        };
      });

      // if (responses[0]?['result']?['code'] == 200) {
      //   setState(() {
      //     pageData['data'] = responses[0]['data'];
      //   });
      // }
      // if (responses[1]?['result']?['code'] == 200) {
      //   setState(() {
      //     pageData['recommend'] = responses[1]['data'];
      //     // I18n.of(context)?.translate('cm_home.recommendGoods');
      //   });
      // }

      // if (responses[2]?['result']?['code'] == 200) {
      //   setState(() {
      //     pageData['cameraInfo'] = {
      //       "tagId": "20000100",
      //       "tagName": I18n.of(context)?.translate('cm_guestHome.addGoods'),
      //       "goodsList": responses[2]['data']['goodsList'].sublist(
      //           0,
      //           responses[2]['data']['goodsList'].length < 10
      //               ? responses[2]['data']['goodsList'].length
      //               : 10),
      //     };
      //   });
      // }

      // if (responses[3]?['result']?['code'] == 200) {
      //   setState(() {
      //     pageData['humidifierInfo'] = {
      //       "tagId": "20000100",
      //       "tagName": I18n.of(context)?.translate('cm_guestHome.addGoods'),
      //       "goodsList": responses[3]['data']['goodsList'].sublist(
      //           0,
      //           responses[3]['data']['goodsList'].length < 10
      //               ? responses[3]['data']['goodsList'].length
      //               : 10),
      //     };
      //   });
      // }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
        onPopInvoked: (result) {
          print("==>>TODO 5522: $result");
        },
        body: Stack(
          children: [
            Column(
              children: [
                SearchHeader(),
                Expanded(
                    child: FutureBuilder(
                        future: _initFuture,
                        builder: (context, snapshot) {
                          return SingleChildScrollView(
                            child: Column(
                              children: [
                                HeadSwiper(),
                                NewsList(),
                                NoticeCard(),
                                // 首屏内容 - 立即加载
                                ProcessCard(),
                                BannerProduct(
                                  banner: yiwuHotBanner,
                                  data: pageData['recommend']
                                      ?['h5YiwuHotSaleGoods'],
                                ),
                                BannerProduct(
                                  banner: mercadoHotBanner,
                                  data: pageData['recommend']
                                      ?['h5MercadoHotSaleGoods'],
                                ),

                                // 非首屏内容 - 延迟加载优化
                                _buildDelayedContent(),
                              ],
                            ),
                          );
                        })),
                AppTabbar(currentIndex: 0)
              ],
            ),
            if (_isLoading)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.2),
                  child: Center(
                    child: RotationalLoadingWidget(),
                  ),
                ),
              ),
          ],
        ));
  }

  @override
  Widget build2(BuildContext context) {
    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            onPopInvoked: (result) {
              print("==>>TODO 5522: $result");
            },
            body: Column(
              children: [
                SearchHeader(),
                Expanded(
                    child: SingleChildScrollView(
                  child: Column(
                    children: [
                      HeadSwiper(),
                      NewsList(),
                      NoticeCard(),
                      // 首屏内容 - 立即加载
                      ProcessCard(),
                      BannerProduct(
                        banner: yiwuHotBanner,
                        data: pageData['recommend']?['h5YiwuHotSaleGoods'],
                      ),
                      BannerProduct(
                        banner: mercadoHotBanner,
                        data: pageData['recommend']?['h5MercadoHotSaleGoods'],
                      ),

                      // 非首屏内容 - 延迟加载优化
                      _buildDelayedContent(),
                    ],
                  ),
                )),
                AppTabbar(currentIndex: 0)
              ],
            ),
          );
  }

  // 延迟加载非首屏内容
  Widget _buildDelayedContent() {
    return FutureBuilder(
      future: Future.delayed(Duration(milliseconds: 200)),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
              ),
            ),
          );
        }
        return Column(
          children: [
            CategoryProduct(
              color: Colors.red.shade500,
              data: pageData['cameraInfo'],
            ),
            CategoryProduct(
              color: Colors.red.shade300,
              data: pageData['humidifierInfo'],
            ),
            _buildFindGoods(),
            ChooseUs(),
            _buildCarouselChoose(),
            _buildRegisterLogin(),
            _buildUserVideos(),
            MyFooter(),
          ],
        );
      },
    );
  }

  Widget _buildFindGoods() {
    return Container(
      width: double.infinity,
      height: 708.sp,
      margin: EdgeInsets.only(top: 60.sp),
      child: Stack(
        children: [
          SvgPicture.asset(
            'assets/images/home/<USER>',
            width: double.infinity,
            height: 708.sp,
            fit: BoxFit.cover,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.only(top: 340.sp),
              child: Container(
                width: double.infinity,
                child: Column(
                  children: [
                    _buildFindGoodsFeatureItem(
                      iconPath: 'assets/images/home/<USER>',
                      text: I18n.of(context)
                              ?.translate('cm_home.quickPreciseSearch') ??
                          I18n.of(context)?.translate('cm_home.quickSearch') ??
                          'Búsqueda Rápida y Precisa',
                    ),
                    SizedBox(height: 30.sp),
                    _buildFindGoodsFeatureItem(
                      iconPath: 'assets/images/home/<USER>',
                      text: I18n.of(context)
                              ?.translate('cm_home.fullSpanishSupport') ??
                          I18n.of(context)
                              ?.translate('cm_home.spanishSupport') ??
                          'Soporte integral en Español',
                    ),
                    SizedBox(height: 70.sp),
                    _buildGuidedSearchButton(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFindGoodsFeatureItem({
    required String iconPath,
    required String text,
  }) {
    return Column(
      children: [
        SvgPicture.asset(
          iconPath,
          width: 34.sp,
          height: 34.sp,
        ),
        SizedBox(height: 12.sp),
        Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            height: 1.5,
            color: const Color(0xFF11263B),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // 引导搜索按钮
  Widget _buildGuidedSearchButton() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(AppRoutes.SearchLooking);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 24.sp,
          vertical: 17.sp,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFF11263B),
          borderRadius: BorderRadius.circular(500),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/images/home/<USER>',
              width: 24.sp,
              height: 24.sp,
            ),

            SizedBox(width: 8.sp),

            Flexible(
              child: Text(
                I18n.of(context)?.translate('cm_home.guidedSearchInit') ??
                    I18n.of(context)?.translate('cm_home.startSearch') ??
                    'Iniciar búsqueda con asistencia',
                style: TextStyle(
                  fontSize: 18.sp,
                  height: 1.0,
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            SizedBox(width: 8.sp),

            // 右箭头图标
            SvgPicture.asset(
              'assets/images/common/arrow-right-white.svg',
              width: 10.sp,
            ),
          ],
        ),
      ),
    );
  }

  // 选择我们轮播图
  Widget _buildCarouselChoose() {
    return Container(
      height: 248,
      padding: EdgeInsets.only(top: 12),
      color: Colors.white,
      child: Swiper(
        autoplay: true,
        autoplayDelay: 4000,
        duration: 750,
        itemCount: carouselChooseData.length,
        itemBuilder: (BuildContext context, int index) {
          return SizedBox(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(2),
              child: MyCachedNetworkImage(
                imageurl: carouselChooseData[index],
              ),
            ),
          );
        },
      ),
    );
  }

  // 注册登录
  Widget _buildRegisterLogin() {
    return Container(
      padding: EdgeInsets.only(top: 12),
      child: Stack(children: [
        CachedNetworkImage(
          imageUrl: loginBg,
          fit: BoxFit.fill,
          width: MediaQuery.of(context).size.width,
          height: 200,
        ),
        Container(
          height: 200,
          width: double.infinity,
          color: Colors.white.withOpacity(0.7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20),
                child: Text(
                  I18n.of(context)!.translate("cm_guestHome.loginTitle"),
                  textAlign: TextAlign.center, // 关键属性
                  maxLines: null, // 允许无限换行
                  softWrap: true, // 允许在单词之间换行
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 8, left: 24, right: 24),
                child: Text(
                  I18n.of(context)!.translate("cm_guestHome.loginDesc"),
                  textAlign: TextAlign.center, // 关键属性
                  maxLines: null, // 允许无限换行
                  softWrap: true, // 允许在单词之间换行
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
              Center(
                child: GestureDetector(
                  onTap: () {
                    AuthHelper.showRegisterModal(
                      context,
                      redirectRoute: AppRoutes.HomePage, // 可选，注册成功后的重定向路由
                      onAuthSuccess: () {
                        // 可选，注册成功后的回调函数
                        print('注册成功');
                      },
                    );
                  },
                  child: Container(
                    width: 152,
                    margin: EdgeInsets.only(top: 12),
                    padding: EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.shade800, // 红色背景
                      borderRadius: BorderRadius.circular(24), // 圆角半径
                    ),
                    child: Center(
                        child: Text(
                      I18n.of(context)!.translate("cm_common.createAccount"),
                      style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500),
                    )),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      I18n.of(context)!.translate("cm_common.haveAccount"),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(left: 6),
                      child: GestureDetector(
                        onTap: () {
                          AuthHelper.showLoginModal(
                            context,
                            redirectRoute: AppRoutes.HomePage, // 可选，登录成功后的重定向路由
                            onAuthSuccess: () {
                              // 可选，登录成功后的回调函数
                              print('登录成功');
                            },
                          );
                        },
                        child: Text(
                          I18n.of(context)!.translate("cm_common.loginAccount"),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        )
      ]),
    );
  }

  // 人们对我们的看法 - 简化版本，使用固定封面图
  Widget _buildUserVideos() {
    // 直接使用userVideoData中的数据

    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            I18n.of(context)!.translate("cm_guestHome.userVideoTitle"),
            textAlign: TextAlign.center,
            maxLines: null,
            softWrap: true,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Container(
            height: 96,
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: userVideoData.length,
              separatorBuilder: (context, index) => SizedBox(width: 12),
              itemBuilder: (context, index) {
                final video = userVideoData[index];
                return _buildSimpleVideoThumbnail(
                    video.videoBg ?? '', video.id ?? '', index);
              },
            ),
          )
        ],
      ),
    );
  }

  // 简化的视频缩略图 - 只显示封面图和播放按钮
  Widget _buildSimpleVideoThumbnail(
      String coverUrl, String videoId, int index) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: SizedBox(
        width: 136,
        height: 96,
        child: InkWell(
          onTap: () => _showVideoModal(videoId),
          child: Stack(
            children: [
              // 视频封面图
              Image.network(
                coverUrl,
                width: 136,
                height: 96,
                fit: BoxFit.cover,
                cacheWidth: 272,
                cacheHeight: 192,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey[300],
                    child: Center(
                      child: CircularProgressIndicator(
                        color: Colors.red,
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) => Container(
                  color: Colors.grey[300],
                  child: Center(
                    child: Icon(
                      Icons.play_circle_outline,
                      color: Colors.grey[600],
                      size: 32,
                    ),
                  ),
                ),
              ),
              // 播放按钮覆盖层
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.black.withOpacity(0.3),
                  ),
                  child: Center(
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示视频模态框
  void _showVideoModal(String videoId) {
    if (videoId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('视频暂时无法播放')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: 250,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Stack(
              children: [
                // YouTube播放器容器
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: YoutubePlayer(
                      controller: YoutubePlayerController(
                        initialVideoId: videoId,
                        flags: YoutubePlayerFlags(
                          autoPlay: true,
                          mute: false,
                        ),
                      ),
                      showVideoProgressIndicator: true,
                      progressIndicatorColor: Colors.red,
                      progressColors: const ProgressBarColors(
                        playedColor: Colors.red,
                        handleColor: Colors.redAccent,
                      ),
                      // 自定义加载指示器
                      bufferIndicator: Center(
                        child: SizedBox(
                          width: 50,
                          height: 50,
                          child: CircularProgressIndicator(
                            color: Colors.red,
                            strokeWidth: 3,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                // 关闭按钮
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class YouTubeVideoItem {
  String? id;
  String? videoBg;
  String? title;

  YouTubeVideoItem({this.id, this.videoBg, this.title});
}

class VideoItem {
  final String id; // YouTube视频ID
  final String title; // 视频标题
  final String thumbnail; // 视频缩略图URL（可选）

  VideoItem({
    required this.id,
    required this.title,
    this.thumbnail = '',
  });
}
